// src/env.ts
import { z } from 'zod'
import dotenv from 'dotenv'

dotenv.config()

const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('3000'),
  DATABASE_URL: z.string(),
  JWT_SECRET: z.string(),
  REDIS_URL: z.string().optional(),
  DEFAULT_TIMEZONE: z.string().default('Asia/Shanghai'),
  REDIS_HOST: z.string().default('localhost'),
  REDIS_PORT: z.string().default('6379'),
  REDIS_PASSWORD: z.string().default(''),
  REDIS_USERNAME: z.string().default('default'),
})

export const env = envSchema.parse(process.env)

export const isDevelopment = env.NODE_ENV == 'development'
