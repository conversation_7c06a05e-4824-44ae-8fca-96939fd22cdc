import type { FastifyTRPCPluginOptions } from '@trpc/server/adapters/fastify'
import { fastifyTRPCPlugin } from '@trpc/server/adapters/fastify'
import Fastify from 'fastify'
import fastifyCookie from '@fastify/cookie'
import fastifyCors from '@fastify/cors'
import { openApiDocument } from './openapi'
import createContext from './context'
import type { AppRouter } from './router'
import { appRouter, openApiRouter } from './router'
import { fastifyTRPCOpenApiPlugin } from 'trpc-to-openapi'
import { env } from './env'

// export const mergeRouters = t.mergeRouters

const isDevelopment = env.NODE_ENV !== 'production'

const app = Fastify({
  logger: {
    level: isDevelopment ? 'info' : 'warn',
    transport: isDevelopment
      ? {
          target: 'pino-pretty',
          options: {
            translateTime: 'SYS:yyyy-mm-dd HH:MM:ss',
            ignore: 'pid,hostname',
            colorize: true,
            levelFirst: true,
            messageFormat: '{msg}',
            singleLine: true,
          },
        }
      : undefined,
  },
})

async function main() {
  await app.register(fastifyCors)

  await app.register(fastifyCookie)

  await app.register(fastifyTRPCPlugin, {
    prefix: '/api/trpc',
    useWSS: false,
    trpcOptions: {
      router: appRouter,
      createContext,
      onError({ path, error }) {
        // report to error monitoring
        console.error(`Error in tRPC handler on path '${path}':`, error)
      },
    } satisfies FastifyTRPCPluginOptions<AppRouter>['trpcOptions'],
  })

  await app.register(fastifyTRPCOpenApiPlugin, {
    basePath: '/api',
    router: openApiRouter,
    createContext,
  })

  app.get('/openapi.json', () => openApiDocument)
  app.get('/panel', async (_, res) => {
    if (env.NODE_ENV !== 'development') {
      return res.status(404).send('Not Found')
    }

    // Dynamically import renderTrpcPanel only in development
    const { renderTrpcPanel } = await import('trpc-ui')

    return res
      .status(200)
      .header('Content-Type', 'text/html')
      .send(
        renderTrpcPanel(appRouter, {
          url: '/api/trpc',
          transformer: 'superjson',
          meta: {
            title: 'trpc-server',
            description: 'trpc-server',
          },
        })
      )
  })

  const port = Number(env.PORT) || 2022 
  await app.listen({
    port,
    host: '0.0.0.0',
  })

  app.log.info(`🚀 服务器启动成功！`)
  app.log.info(`📡 API 地址: http://localhost:${port}/trpc`)
  app.log.info(`🎛️  控制面板: http://localhost:${port}/panel`)
  app.log.info(`🌍 环境: ${env.NODE_ENV}`)
}

main().catch((err) => {
  app.log.error(err, '❌ 服务器启动失败')
  console.error(err)
  process.exit(1)
})
