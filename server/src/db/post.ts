import { pgTable } from 'drizzle-orm/pg-core'
import { createInsertSchema, createSelectSchema } from 'drizzle-zod'
import z from 'zod'
import { timestamps } from './columns.helpers'

export const post = pgTable('post', (t) => ({
  id: t.uuid().notNull().primaryKey().defaultRandom(),
  title: t.varchar({ length: 256 }).notNull(),
  content: t.text().notNull(),
  ...timestamps,
}))

// 类型推导
export type Post = typeof post.$inferSelect
export type InsertPost = typeof post.$inferInsert

export const CreatePostSchema = createInsertSchema(post, {
  title: z.string().max(50, '文章标题不能超过50个字').describe('文章标题'),
  content: z.string().describe('文章内容'),
}).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
})

export const SelectPostSchema = createSelectSchema(post)
