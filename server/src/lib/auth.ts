import bcrypt from 'bcryptjs'
import { db } from '@/db'
import { users } from '@/db/schema'
import type { User } from '@/db/schema'
import { eq } from 'drizzle-orm'
import type { JWTPayload } from './jwt'
import { generateToken } from './jwt'
import { VerificationCodeManager } from './redis'
import { env } from '../env'

// 不返回密码的用户类型
export type SafeUser = Omit<User, 'password'>

/**
 * 生成默认头像URL
 */
function generateAvatarUrl(userId: string): string {
  // 使用 DiceBear API 生成头像
  const style = 'avataaars' // 可选风格：avataaars, bottts, pixel-art 等
  const encodedSeed = encodeURIComponent(userId)
  return `https://api.dicebear.com/7.x/${style}/svg?seed=${encodedSeed}&backgroundColor=b6e3f4`
}

/**
 * 密码加密
 */
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12)
}

/**
 * 密码验证
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash)
}

/**
 * 发送短信验证码 (使用Redis存储)
 */
export async function sendSMSCode(
  phone: string,
  type: 'login' | 'register' | 'reset_password' = 'login'
): Promise<{ code: string; success: boolean }> {
  // 检查发送频率限制
  const rateCheck = await VerificationCodeManager.checkSendRate(phone)
  if (!rateCheck.canSend) {
    throw new Error(`请等待${rateCheck.remainingTime}秒后再试`)
  }

  // 生成验证码并存储到Redis
  const code = await VerificationCodeManager.generateCode(phone, type)

  // 开发环境直接返回验证码，生产环境应该发送真实短信
  if (env.NODE_ENV === 'development') {
    console.log(`📱 短信验证码发送到 ${phone}: ${code}`)
    return { code, success: true }
  }

  // 生产环境发送真实短信的逻辑
  try {
    // TODO: 集成真实短信服务提供商
    // await sendRealSMS(phone, code)
    return { code: '', success: true }
  } catch (error) {
    console.error('短信发送失败:', error)
    return { code: '', success: false }
  }
}

/**
 * 验证短信验证码 (使用Redis验证)
 */
export async function verifySMSCode(
  phone: string,
  code: string,
  type: 'login' | 'register' | 'reset_password' = 'login'
): Promise<boolean> {
  const result = await VerificationCodeManager.verifyCode(phone, type, code)
  return result.success
}

/**
 * 手机号登录/注册
 */
export async function loginOrRegisterWithPhone(
  phone: string,
  code: string
): Promise<{ user: SafeUser; token: string; isNewUser: boolean }> {
  // 验证短信验证码
  const isValidCode = await verifySMSCode(phone, code, 'login')
  if (!isValidCode) {
    throw new Error('验证码错误或已过期')
  }

  // 查找用户
  let user = await db.query.users.findFirst({
    where: eq(users.phone, phone),
  })

  let isNewUser = false

  if (!user) {
    // 新用户，自动注册
    const newUser = await db
      .insert(users)
      .values({
        phone,
        phoneVerified: true,
        avatar: generateAvatarUrl(phone),
        name: phone, // 默认用手机号作为姓名
      })
      .returning()

    user = newUser[0]!
    isNewUser = true
  }

  // 去除密码字段
  const { password: _, ...safeUser } = user

  // 生成JWT token
  const jwtPayload: JWTPayload = {
    userId: user.id,
    phone: user.phone ?? '',
  }

  const token = generateToken(jwtPayload)

  return { user: safeUser, token, isNewUser }
}

/**
 * 邮箱/手机号密码登录
 */
export async function loginWithPassword(
  emailOrPhone: string,
  password: string
): Promise<{ user: SafeUser; token: string }> {
  // 检查是否是邮箱格式
  const isEmail = emailOrPhone.includes('@')

  const user = await db.query.users.findFirst({
    where: isEmail ? eq(users.email, emailOrPhone) : eq(users.phone, emailOrPhone),
  })

  if (!user) {
    throw new Error('用户不存在')
  }

  if (!user.password) {
    throw new Error('该账号未设置密码，请使用手机号验证码登录')
  }

  // 验证密码
  const isValidPassword = await verifyPassword(password, user.password)
  if (!isValidPassword) {
    throw new Error('密码错误')
  }

  // 去除密码字段
  const { password: _, ...safeUser } = user

  // 生成JWT token
  const jwtPayload: JWTPayload = {
    userId: user.id,
    phone: user.phone ?? '',
  }

  const token = generateToken(jwtPayload)

  return { user: safeUser, token }
}

/**
 * 账号密码注册
 */
export async function registerWithPassword(data: {
  email?: string
  phone?: string
  password: string
  name?: string
}): Promise<{ user: SafeUser; token: string }> {
  const { email, phone, password, name } = data

  // 验证必须提供邮箱或手机号
  if (!email && !phone) {
    throw new Error('请提供邮箱或手机号')
  }

  // 检查用户是否已存在
  if (email) {
    const existingUser = await db.query.users.findFirst({
      where: eq(users.email, email),
    })
    if (existingUser) {
      throw new Error('该邮箱已被注册')
    }
  }

  if (phone) {
    const existingUser = await db.query.users.findFirst({
      where: eq(users.phone, phone),
    })
    if (existingUser) {
      throw new Error('该手机号已被注册')
    }
  }

  // 密码加密
  const hashedPassword = await hashPassword(password)

  // 创建用户
  const newUser = await db
    .insert(users)
    .values({
      email: email ?? null,
      phone: phone ?? null,
      password: hashedPassword,
      name: name ?? email ?? phone ?? '用户',
      phoneVerified: false,
      emailVerified: false,
    })
    .returning()

  const user = newUser[0]!

  // 生成并更新默认头像
  const avatarUrl = generateAvatarUrl(user.id)
  await db.update(users).set({ avatar: avatarUrl }).where(eq(users.id, user.id))

  // 更新用户对象以包含头像URL
  user.avatar = avatarUrl

  // 去除密码字段
  const { password: _, ...safeUser } = user

  // 生成JWT token
  const jwtPayload: JWTPayload = {
    userId: user.id,
    phone: user.phone ?? '',
  }

  const token = generateToken(jwtPayload)

  return { user: safeUser, token }
}
