import { z } from 'zod'

// 通用的应用ID输入schema
export const ApplicationIdSchema = z.object({
  applicationId: z.string().uuid('无效的应用ID'),
})

// 分页参数schema
export const PaginationSchema = z.object({
  page: z.number().min(1, '页码必须大于0').default(1),
  pageSize: z.number().min(1, '每页数量必须大于0').max(100, '每页数量不能超过100').default(10),
})

// 应用ID + 分页参数
export const ApplicationPaginationSchema = ApplicationIdSchema.extend({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
})

// 金额相关schema
export const AmountSchema = z.object({
  amount: z.number().min(0.01, '金额必须大于0'),
})

// 应用余额检查schema
export const BalanceCheckSchema = ApplicationIdSchema.extend({
  amount: z.number().min(0.01, '金额必须大于0'),
})

// 日期范围schema
export const DateRangeSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// 类型导出
export type ApplicationIdInput = z.infer<typeof ApplicationIdSchema>
export type PaginationInput = z.infer<typeof PaginationSchema>
export type ApplicationPaginationInput = z.infer<typeof ApplicationPaginationSchema>
export type AmountInput = z.infer<typeof AmountSchema>
export type BalanceCheckInput = z.infer<typeof BalanceCheckSchema>
export type DateRangeInput = z.infer<typeof DateRangeSchema> 