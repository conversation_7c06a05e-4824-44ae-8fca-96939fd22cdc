import { db } from '@/db'
import { orders, OrderType, OrderStatus } from '@/db/order'
import { eq, desc, and, gte, lte, like, count, sum, sql } from 'drizzle-orm'
import { nanoid } from 'nanoid'
import { rechargeBalance } from './balance'

/**
 * 生成订单号
 */
function generateOrderNo(): string {
  const timestamp = Date.now()
  const random = nanoid(8)
  return `ORD${timestamp}${random}`
}

/**
 * 创建充值订单并直接完成充值
 */
export async function createRechargeOrder(
  userId: string,
  applicationId: string, // 必须指定应用
  amount: number,
  type: keyof typeof OrderType = OrderType.GIFT,
  remarks?: string
): Promise<string> {
  const orderNo = generateOrderNo()
  const antCoins = amount // 1元 = 1蚁贝

  await db.transaction(async (tx) => {
    // 创建订单
    const [order] = await tx
      .insert(orders)
      .values({
        orderNo,
        userId,
        applicationId,
        antCoins: antCoins.toFixed(2),
        amount: amount.toFixed(2),
        type,
        status: OrderStatus.COMPLETED, // 直接完成
        remarks,
      })
      .returning({
        id: orders.id,
      })

    // 直接充值到应用余额
    await rechargeBalance(
      applicationId,
      userId,
      antCoins,
      order!.id,
      `${type === OrderType.GIFT ? '赠送' : '购买'}充值 - 订单号: ${orderNo}`
    )
  })

  return orderNo
}

/**
 * 获取订单列表
 */
export async function getOrderList(params: {
  page?: number
  pageSize?: number
  userId?: string
  status?: keyof typeof OrderStatus
  type?: keyof typeof OrderType
  startDate?: string
  endDate?: string
  search?: string
}) {
  const { page = 1, pageSize = 10, userId, status, type, startDate, endDate, search } = params

  const offset = (page - 1) * pageSize

  // 构建查询条件
  const conditions = []
  if (userId) conditions.push(eq(orders.userId, userId))
  if (status) conditions.push(eq(orders.status, status))
  if (type) conditions.push(eq(orders.type, type))
  if (startDate) conditions.push(gte(orders.createdAt, startDate))
  if (endDate) conditions.push(lte(orders.createdAt, endDate))
  if (search) conditions.push(like(orders.orderNo, `%${search}%`))

  const whereClause = conditions.length > 0 ? and(...conditions) : undefined

  // 获取总数
  const [totalResult] = await db.select({ count: count() }).from(orders).where(whereClause)

  // 获取订单列表
  const orderList = await db.query.orders.findMany({
    where: whereClause,
    orderBy: [desc(orders.createdAt)],
    limit: pageSize,
    offset,
  })

  return {
    data: orderList,
    total: totalResult?.count || 0,
    page,
    pageSize,
  }
}

/**
 * 获取订单详情
 */
export async function getOrderById(orderId: string) {
  const order = await db.query.orders.findFirst({
    where: eq(orders.id, orderId),
  })

  return order
}

/**
 * 更新订单状态
 */
export async function updateOrderStatus(
  orderId: string,
  status: keyof typeof OrderStatus,
  remarks?: string
): Promise<void> {
  const updateData: { status: string; remarks?: string } = { status }
  if (remarks) updateData.remarks = remarks

  await db.update(orders).set(updateData).where(eq(orders.id, orderId))
}

/**
 * 申请发票
 */
export async function requestInvoice(orderId: string): Promise<void> {
  await db.update(orders).set({ invoiceRequested: true }).where(eq(orders.id, orderId))
}

/**
 * 取消订单
 */
export async function cancelOrder(orderId: string): Promise<void> {
  await updateOrderStatus(orderId, OrderStatus.CANCELLED, '订单已取消')
}

/**
 * 获取用户订单统计
 */
export async function getUserOrderStats(userId: string) {
  const [totalOrders] = await db.select({ count: count() }).from(orders).where(eq(orders.userId, userId))

  const [completedOrders] = await db
    .select({ count: count() })
    .from(orders)
    .where(and(eq(orders.userId, userId), eq(orders.status, OrderStatus.COMPLETED)))

  const [totalAmount] = await db
    .select({
      total: sum(sql`CAST(${orders.amount} AS DECIMAL)`),
    })
    .from(orders)
    .where(and(eq(orders.userId, userId), eq(orders.status, OrderStatus.COMPLETED)))

  return {
    totalOrders: totalOrders?.count || 0,
    completedOrders: completedOrders?.count || 0,
    totalAmount: totalAmount?.total || 0,
  }
}
