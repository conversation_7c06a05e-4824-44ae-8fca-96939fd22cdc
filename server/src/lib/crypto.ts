import bcrypt from 'bcrypt'
import { randomBytes } from 'crypto'

const SALT_ROUNDS = 12

/**
 * 生成随机 Secret
 */
export function generateSecret(): string {
  // 生成 32 位随机字符串，使用 base64 编码
  return randomBytes(24).toString('base64url') // base64url 避免特殊字符
}

/**
 * 哈希 Secret
 */
export async function hashSecret(secret: string): Promise<string> {
  return bcrypt.hash(secret, SALT_ROUNDS)
}

/**
 * 验证 Secret
 */
export async function verifySecret(secret: string, hashedSecret: string): Promise<boolean> {
  return bcrypt.compare(secret, hashedSecret)
}

/**
 * 格式化 Secret 显示（只显示前4位和后4位）
 */
export function formatSecretForDisplay(secret: string): string {
  if (secret.length < 8) {
    return '*'.repeat(secret.length)
  }
  const start = secret.slice(0, 4)
  const end = secret.slice(-4)
  const middle = '*'.repeat(Math.max(8, secret.length - 8))
  return `${start}${middle}${end}`
} 