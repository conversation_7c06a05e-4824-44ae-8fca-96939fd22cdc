import { db } from '@/db'
import type { NewTransaction } from '@/db/balance'
import { applicationBalances, transactions, apiCalls, TransactionType, ApiCostType } from '@/db/balance'
import { eq, desc, and, gte, lte } from 'drizzle-orm'

/**
 * 获取应用蚁贝余额
 */
export async function getApplicationBalance(applicationId: string): Promise<string> {
  const balance = await db.query.applicationBalances.findFirst({
    where: eq(applicationBalances.applicationId, applicationId),
  })

  if (!balance) {
    // 如果应用没有余额记录，创建一个初始余额为0的记录
    await db.insert(applicationBalances).values({
      applicationId,
      balance: '0.00',
    })
    return '0.00'
  }

  return balance.balance
}

/**
 * 创建交易记录并更新应用余额
 */
export async function createTransaction(
  applicationId: string,
  userId: string,
  type: keyof typeof TransactionType,
  amount: number,
  relatedId: string,
  relatedType: 'order' | 'api_call' | 'refund' = 'order',
  description?: string
): Promise<void> {
  await db.transaction(async (tx) => {
    // 获取当前余额
    const currentBalance = await getApplicationBalance(applicationId)
    const beforeBalance = parseFloat(currentBalance)

    // 计算新余额
    let afterBalance: number
    if (type === TransactionType.RECHARGE) {
      afterBalance = beforeBalance + amount
    } else if (type === TransactionType.CONSUME) {
      afterBalance = beforeBalance - amount
      if (afterBalance < 0) {
        throw new Error('余额不足')
      }
    } else {
      // TransactionType.REFUND
      afterBalance = beforeBalance + amount
    }

    // 插入交易记录
    await tx.insert(transactions).values({
      applicationId,
      userId,
      type,
      amount: (type === TransactionType.CONSUME ? -amount : amount).toFixed(2),
      beforeBalance: beforeBalance.toFixed(2),
      afterBalance: afterBalance.toFixed(2),
      description,
      relatedId,
      relatedType,
    } satisfies NewTransaction)

    // 更新应用余额
    await tx
      .insert(applicationBalances)
      .values({
        applicationId,
        balance: afterBalance.toFixed(2),
      })
      .onConflictDoUpdate({
        target: applicationBalances.applicationId,
        set: {
          balance: afterBalance.toFixed(2),
          updatedAt: new Date().toISOString(),
        },
      })
  })
}

/**
 * 充值蚁贝到应用
 */
export async function rechargeBalance(
  applicationId: string,
  userId: string,
  amount: number,
  relatedId: string,
  description?: string
): Promise<void> {
  await createTransaction(applicationId, userId, TransactionType.RECHARGE, amount, relatedId, 'order', description)
}

/**
 * 从应用消费蚁贝
 */
export async function consumeBalance(
  applicationId: string,
  userId: string,
  amount: number,
  relatedId: string,
  description?: string
): Promise<void> {
  await createTransaction(applicationId, userId, TransactionType.CONSUME, amount, relatedId, 'api_call', description)
}

/**
 * 退款蚁贝到应用
 */
export async function refundBalance(
  applicationId: string,
  userId: string,
  amount: number,
  relatedId: string,
  description?: string
): Promise<void> {
  await createTransaction(applicationId, userId, TransactionType.REFUND, amount, relatedId, 'order', description)
}

/**
 * 获取应用交易记录
 */
export async function getApplicationTransactions(applicationId: string, page: number = 1, pageSize: number = 10) {
  const offset = (page - 1) * pageSize

  const records = await db.query.transactions.findMany({
    where: eq(transactions.applicationId, applicationId),
    orderBy: [desc(transactions.createdAt)],
    limit: pageSize,
    offset,
  })

  return records
}

/**
 * 检查应用余额是否足够
 */
export async function checkApplicationBalance(applicationId: string, requiredAmount: number): Promise<boolean> {
  const balance = await getApplicationBalance(applicationId)
  return parseFloat(balance) >= requiredAmount
}

/**
 * 记录API调用并扣费
 */
export async function recordApiCall(
  applicationId: string,
  endpoint: string,
  method: string,
  costType: keyof typeof ApiCostType,
  costAmount: number,
  requestSize?: number,
  responseSize?: number,
  statusCode?: number
): Promise<void> {
  await db.transaction(async (tx) => {
    // 记录API调用
    await tx.insert(apiCalls).values({
      applicationId,
      endpoint,
      method,
      costType,
      costAmount: costAmount.toFixed(2),
      requestSize,
      responseSize,
      statusCode,
    })

    // 如果有费用，则扣费
    if (costAmount > 0) {
      // 先检查余额是否足够
      const sufficient = await checkApplicationBalance(applicationId, costAmount)
      if (!sufficient) {
        throw new Error('应用余额不足，无法完成API调用')
      }

      // 扣费
      const description = `API调用扣费 - ${endpoint} (${costType === ApiCostType.ACCOUNT_QUOTA ? '账号额度' : '流量'})`
      await consumeBalance(applicationId, 'system', costAmount, 'api_call', description)
    }
  })
}

/**
 * 获取API调用记录
 */
export async function getApiCalls(
  applicationId: string,
  page: number = 1,
  pageSize: number = 10,
  filters?: {
    endpoint?: string
    costType?: keyof typeof ApiCostType
    startDate?: string
    endDate?: string
  }
) {
  const offset = (page - 1) * pageSize

  // 构建查询条件
  const conditions = [eq(apiCalls.applicationId, applicationId)]
  if (filters?.endpoint) {
    conditions.push(eq(apiCalls.endpoint, filters.endpoint))
  }
  if (filters?.costType) {
    conditions.push(eq(apiCalls.costType, filters.costType))
  }
  if (filters?.startDate) {
    conditions.push(gte(apiCalls.createdAt, filters.startDate))
  }
  if (filters?.endDate) {
    conditions.push(lte(apiCalls.createdAt, filters.endDate))
  }

  const whereClause = conditions.length > 1 ? and(...conditions) : conditions[0]

  const records = await db.query.apiCalls.findMany({
    where: whereClause,
    orderBy: [desc(apiCalls.createdAt)],
    limit: pageSize,
    offset,
  })

  return records
}

/**
 * 计算购买账号额度所需蚁贝
 */
export function calculateAccountQuotaCost(quotaCount: number): number {
  return quotaCount * 40 // 1个账号额度 = 40蚁贝
}

/**
 * 计算购买流量所需蚁贝
 */
export function calculateTrafficCost(trafficGB: number): number {
  return trafficGB * 1 // 1GB流量 = 1蚁贝
}

/**
 * 自动扣费：账号额度
 */
export async function autoDeductAccountQuota(
  applicationId: string,
  quotaCount: number,
  endpoint: string,
  method: string = 'POST'
): Promise<void> {
  const cost = calculateAccountQuotaCost(quotaCount)
  await recordApiCall(applicationId, endpoint, method, ApiCostType.ACCOUNT_QUOTA, cost, undefined, undefined, 200)
}

/**
 * 自动扣费：流量
 */
export async function autoDeductTraffic(
  applicationId: string,
  trafficGB: number,
  endpoint: string,
  method: string = 'GET',
  requestSize?: number,
  responseSize?: number,
  statusCode?: number
): Promise<void> {
  const cost = calculateTrafficCost(trafficGB)
  await recordApiCall(applicationId, endpoint, method, ApiCostType.TRAFFIC, cost, requestSize, responseSize, statusCode)
}
