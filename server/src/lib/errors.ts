import { TRPCError } from '@trpc/server'

/**
 * 余额相关错误类
 */
export class BalanceError extends Error {
  constructor(
    message: string,
    public code: 'INSUFFICIENT_BALANCE' | 'INVALID_AMOUNT' | 'TRANSACTION_FAILED' | 'APPLICATION_NOT_FOUND',
    public applicationId?: string,
    public userId?: string
  ) {
    super(message)
    this.name = 'BalanceError'
  }

  /**
   * 转换为tRPC错误
   */
  toTRPCError(): TRPCError {
    switch (this.code) {
      case 'INSUFFICIENT_BALANCE':
        return new TRPCError({
          code: 'BAD_REQUEST',
          message: this.message,
        })
      case 'INVALID_AMOUNT':
        return new TRPCError({
          code: 'BAD_REQUEST',
          message: this.message,
        })
      case 'APPLICATION_NOT_FOUND':
        return new TRPCError({
          code: 'NOT_FOUND',
          message: this.message,
        })
      case 'TRANSACTION_FAILED':
      default:
        return new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: this.message,
        })
    }
  }
}

/**
 * 应用相关错误类
 */
export class ApplicationError extends Error {
  constructor(
    message: string,
    public code: 'NOT_FOUND' | 'PERMISSION_DENIED' | 'CREATION_FAILED' | 'UPDATE_FAILED',
    public applicationId?: string,
    public userId?: string
  ) {
    super(message)
    this.name = 'ApplicationError'
  }

  toTRPCError(): TRPCError {
    switch (this.code) {
      case 'NOT_FOUND':
        return new TRPCError({
          code: 'NOT_FOUND',
          message: this.message,
        })
      case 'PERMISSION_DENIED':
        return new TRPCError({
          code: 'FORBIDDEN',
          message: this.message,
        })
      case 'CREATION_FAILED':
      case 'UPDATE_FAILED':
      default:
        return new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: this.message,
        })
    }
  }
}

/**
 * 订单相关错误类
 */
export class OrderError extends Error {
  constructor(
    message: string,
    public code: 'NOT_FOUND' | 'PERMISSION_DENIED' | 'INVALID_STATUS' | 'CREATION_FAILED',
    public orderId?: string,
    public userId?: string
  ) {
    super(message)
    this.name = 'OrderError'
  }

  toTRPCError(): TRPCError {
    switch (this.code) {
      case 'NOT_FOUND':
        return new TRPCError({
          code: 'NOT_FOUND',
          message: this.message,
        })
      case 'PERMISSION_DENIED':
        return new TRPCError({
          code: 'FORBIDDEN',
          message: this.message,
        })
      case 'INVALID_STATUS':
        return new TRPCError({
          code: 'BAD_REQUEST',
          message: this.message,
        })
      case 'CREATION_FAILED':
      default:
        return new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: this.message,
        })
    }
  }
} 