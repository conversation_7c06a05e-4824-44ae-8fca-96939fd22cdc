import { postRouter } from '@/router/post'
import { authRouter } from '@/router/auth'
import { userRouter } from '@/router/user'
import { applicationRouter } from '@/router/application'
import { balanceRouter } from '@/router/balance'
import { orderRouter } from '@/router/order'
import { openAuthRouter } from '@/router/openAuth'
import { router } from '@/trpc'

export const appRouter = router({
  auth: authRouter,
  post: postRouter,
  user: userRouter,
  application: applicationRouter,
  balance: balanceRouter,
  order: orderRouter,
})

export const openApiRouter = router({
  auth: openAuthRouter,
  post: postRouter,
})

// export type definition of API
export type AppRouter = typeof appRouter
