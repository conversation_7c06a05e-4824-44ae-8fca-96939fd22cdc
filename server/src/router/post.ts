import type { TRPCRouterRecord } from '@trpc/server'
import { z } from 'zod'

import { desc, eq } from 'drizzle-orm'
import { CreatePostSchema, post, SelectPostSchema } from '@/db/schema'

import { publicProcedure } from '../trpc'

export const postRouter = {
  all: publicProcedure
    .meta({
      openapi: {
        method: 'GET',
        path: '/posts',
        summary: '文章列表',
        description: '获取文章列表',
      },
    })
    .input(z.object({}).optional().describe('Request body input'))
    .output(z.array(z.record(z.string(), z.any())))
    .query(({ ctx }) => {
      return ctx.db.query.post.findMany({
        orderBy: desc(post.id),
        limit: 10,
      })
    }),

  byId: publicProcedure
    .meta({
      openapi: {
        method: 'GET',
        path: '/posts/{id}',
        summary: '文章详情',
        description: '获取文章详情',
      },
    })
    .input(z.object({ id: z.string().describe('文章ID') }))
    .output(SelectPostSchema.or(z.undefined()))
    .query(({ ctx, input }) => {
      return ctx.db.query.post.findFirst({
        where: eq(post.id, input.id),
      })
    }),

  create: publicProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/posts',
        summary: '创建文章',
      },
    })
    .input(CreatePostSchema)
    .output(z.record(z.string(), z.any()))
    .mutation(({ ctx, input }) => {
      return ctx.db.insert(post).values(input)
    }),

  delete: publicProcedure
    .meta({
      openapi: {
        method: 'DELETE',
        path: '/posts/{id}',
        summary: '删除文章',
        description: '删除文章',
      },
    })
    .input(z.object({ id: z.string().describe('文章ID') }))
    .output(z.record(z.string(), z.any()))
    .mutation(({ ctx, input }) => {
      return ctx.db.delete(post).where(eq(post.id, input.id))
    }),

  update: publicProcedure
    .meta({
      openapi: {
        method: 'PUT',
        path: '/posts/{id}',
        summary: '更新文章',
        description: '更新文章',
      },
    })
    .input(
      z
        .object({
          id: z.string().describe('文章ID'),
        })
        .merge(CreatePostSchema.partial())
    )
    .output(z.record(z.string(), z.any()))
    .mutation(({ ctx, input }) => {
      return ctx.db.update(post).set(input).where(eq(post.id, input.id))
    }),
} satisfies TRPCRouterRecord
