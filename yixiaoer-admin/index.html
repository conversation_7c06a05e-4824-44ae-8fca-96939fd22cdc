<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蚁小二开放平台管理后台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.css" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .sidebar-item.active {
            background-color: #3b82f6;
            color: white;
        }
        .sidebar-item:hover {
            background-color: #e5e7eb;
        }
        .sidebar-item.active:hover {
            background-color: #2059d2;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 登录页面 -->
    <div id="loginPage" class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900">蚁小二开放平台管理后台</h2>
                <p class="mt-2 text-sm text-gray-600">请登录您的管理员账户</p>
            </div>
            
            <div class="bg-white shadow-md rounded-lg p-8">
                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">手机号</label>
                        <input type="tel" id="loginPhone" placeholder="请输入手机号" 
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">密码</label>
                        <input type="password" id="loginPassword" placeholder="请输入密码" 
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <button onclick="login()" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        登录
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 后台管理页面 -->
    <div id="adminPage" class="hidden min-h-screen bg-gray-50 flex">
        <!-- 左侧边栏 -->
        <div class="w-48 bg-white shadow-md">
            <div class="p-6 border-b border-gray-200">
                <h1 class="text-xl font-bold text-gray-900">蚁小二管理后台</h1>
            </div>
            <nav class="mt-6">
                <div class="px-3 space-y-1">
                    <button onclick="showModule('users')" id="usersNav" class="sidebar-item active w-full flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i data-lucide="users" class="w-5 h-5 mr-3"></i>
                        用户
                    </button>
                    <button onclick="showModule('orders')" id="ordersNav" class="sidebar-item w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md">
                        <i data-lucide="shopping-cart" class="w-5 h-5 mr-3"></i>
                        订单
                    </button>
                </div>
            </nav>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 overflow-auto">
            <!-- 用户模块 -->
            <div id="usersModule" class="p-6">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">用户管理</h2>
                    
                    <!-- 搜索和筛选 -->
                    <div class="bg-white p-4 rounded-lg shadow mb-6">
                        <div class="flex flex-wrap gap-4">
                            <div class="flex-1 min-w-64">
                                <label class="block text-sm font-medium text-gray-700 mb-1">手机号搜索</label>
                                <input type="tel" id="phoneSearch" placeholder="请输入手机号" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div class="flex-1 min-w-48">
                                <label class="block text-sm font-medium text-gray-700 mb-1">注册时间</label>
                                <input type="date" id="dateFilter" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div class="flex items-end">
                                <button onclick="searchUsers()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                    <i data-lucide="search" class="w-4 h-4 mr-2 inline"></i>
                                    搜索
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 用户列表 -->
                    <div class="bg-white shadow rounded-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">手机号</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">注册时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">应用数</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200" id="usersTableBody">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">***********</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 10:30:25</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <button onclick="showUserApps('***********')" class="text-blue-600 hover:text-blue-900 font-medium">
                                            2
                                        </button>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="text" placeholder="添加备注" value="VIP用户"
                                               class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500">
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">***********</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-14 15:20:10</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <button onclick="showUserApps('***********')" class="text-blue-600 hover:text-blue-900 font-medium">
                                            1
                                        </button>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="text" placeholder="添加备注"
                                               class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500">
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">***********</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-13 09:45:30</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <button onclick="showUserApps('***********')" class="text-blue-600 hover:text-blue-900 font-medium">
                                            3
                                        </button>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="text" placeholder="添加备注" value="测试账户"
                                               class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500">
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 订单模块 -->
            <div id="ordersModule" class="hidden p-6">
                <div class="mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-2xl font-bold text-gray-900">订单管理</h2>
                        <button onclick="showCreateOrderModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                            创建订单
                        </button>
                    </div>

                    <!-- 订单列表 -->
                    <div class="bg-white shadow rounded-lg overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单号</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">手机号</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单来源</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">购买数量</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">付款金额</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">应用信息</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">付款方式</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单状态</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单类型</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完成时间</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200" id="ordersTableBody">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">ORD202401150001</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">***********</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">系统订单</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">100 蚁贝</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥100.00</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">测试应用1 (app_123)</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">微信支付</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">已完成</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">购买</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 14:30:25</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <button onclick="showOrderDetail('ORD202401150001')" class="text-gray-400 hover:text-gray-600">
                                                <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">ORD202401140002</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">***********</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">线上订单</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">50 蚁贝</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥0.00</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">测试应用2 (app_456)</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">-</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">已完成</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">赠送</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-14 16:20:10</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <button onclick="showOrderDetail('ORD202401140002')" class="text-gray-400 hover:text-gray-600">
                                                <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 应用列表页面 -->
    <div id="appsListPage" class="hidden min-h-screen bg-gray-50 flex">
        <!-- 左侧边栏 -->
        <div class="w-48 bg-white shadow-md">
            <div class="p-6 border-b border-gray-200">
                <h1 class="text-xl font-bold text-gray-900">蚁小二管理后台</h1>
            </div>
            <nav class="mt-6">
                <div class="px-3 space-y-1">
                    <button onclick="showModule('users')" id="usersNavApps" class="sidebar-item active w-full flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i data-lucide="users" class="w-5 h-5 mr-3"></i>
                        用户
                    </button>
                    <button onclick="showModule('orders')" id="ordersNavApps" class="sidebar-item w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md">
                        <i data-lucide="shopping-cart" class="w-5 h-5 mr-3"></i>
                        订单
                    </button>
                </div>
            </nav>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 overflow-auto">
            <div class="p-6">
                <!-- 面包屑导航 -->
                <nav class="flex mb-4" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <button onclick="backToAdmin()" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                                <i data-lucide="users" class="w-4 h-4 mr-2"></i>
                                用户管理
                            </button>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i data-lucide="chevron-right" class="w-4 h-4 text-gray-400"></i>
                                <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">应用列表</span>
                            </div>
                        </li>
                    </ol>
                </nav>

                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2" id="appsListTitle">用户应用列表</h2>
                    <p class="text-gray-600" id="appsListSubtitle">用户：***********</p>
                </div>

                <!-- 应用列表 -->
                <div class="bg-white shadow rounded-lg overflow-hidden">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">应用名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">应用ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">蚁贝余额</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">账号数</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">流量使用情况</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="appsListTableBody">
                            <!-- 动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- API调用记录页面 -->
    <div id="apiCallsPage" class="hidden min-h-screen bg-gray-50 flex">
        <!-- 左侧边栏 -->
        <div class="w-48 bg-white shadow-md">
            <div class="p-6 border-b border-gray-200">
                <h1 class="text-xl font-bold text-gray-900">蚁小二管理后台</h1>
            </div>
            <nav class="mt-6">
                <div class="px-3 space-y-1">
                    <button onclick="showModule('users')" id="usersNavApi" class="sidebar-item active w-full flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i data-lucide="users" class="w-5 h-5 mr-3"></i>
                        用户
                    </button>
                    <button onclick="showModule('orders')" id="ordersNavApi" class="sidebar-item w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md">
                        <i data-lucide="shopping-cart" class="w-5 h-5 mr-3"></i>
                        订单
                    </button>
                </div>
            </nav>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 overflow-auto">
            <div class="p-6">
                <!-- 面包屑导航 -->
                <nav class="flex mb-4" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <button onclick="backToAdmin()" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                                <i data-lucide="users" class="w-4 h-4 mr-2"></i>
                                用户管理
                            </button>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i data-lucide="chevron-right" class="w-4 h-4 text-gray-400"></i>
                                <button onclick="backToAppsList()" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">应用列表</button>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i data-lucide="chevron-right" class="w-4 h-4 text-gray-400"></i>
                                <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">API调用记录</span>
                            </div>
                        </li>
                    </ol>
                </nav>

                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2" id="apiCallsTitle">API调用记录</h2>
                    <p class="text-gray-600" id="apiCallsSubtitle">应用：测试应用1 (app_123)</p>
                </div>

                <!-- 筛选条件 -->
                <div class="bg-white p-4 rounded-lg shadow mb-6">
                    <div class="flex flex-wrap gap-4">
                        <div class="flex-1 min-w-48">
                            <label class="block text-sm font-medium text-gray-700 mb-1">能力类型</label>
                            <select id="capabilityFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <option value="">全部能力</option>
                                <option value="auth">账号授权</option>
                                <option value="publish">一键发布</option>
                                <option value="message">获取私信</option>
                                <option value="reply">回复私信</option>
                                <option value="analytics">数据分析</option>
                            </select>
                        </div>
                        <div class="flex-1 min-w-48">
                            <label class="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
                            <input type="datetime-local" id="startTimeFilter"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div class="flex-1 min-w-48">
                            <label class="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
                            <input type="datetime-local" id="endTimeFilter"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div class="flex items-end">
                            <button onclick="filterApiCalls()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 mr-2">
                                <i data-lucide="search" class="w-4 h-4 mr-2 inline"></i>
                                筛选
                            </button>
                            <button onclick="resetApiFilters()" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                                <i data-lucide="refresh-cw" class="w-4 h-4 mr-2 inline"></i>
                                重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- API调用记录列表 -->
                <div class="bg-white shadow rounded-lg overflow-hidden">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">调用时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">能力类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">API端点</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">请求方法</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态码</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">消耗蚁贝</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">响应时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP地址</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="apiCallsTableBody">
                            <!-- 动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 流量使用详情页面 -->
    <div id="trafficDetailsPage" class="hidden min-h-screen bg-gray-50 flex">
        <!-- 左侧边栏 -->
        <div class="w-48 bg-white shadow-md">
            <div class="p-6 border-b border-gray-200">
                <h1 class="text-xl font-bold text-gray-900">蚁小二管理后台</h1>
            </div>
            <nav class="mt-6">
                <div class="px-3 space-y-1">
                    <button onclick="showModule('users')" id="usersNavTraffic" class="sidebar-item active w-full flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <i data-lucide="users" class="w-5 h-5 mr-3"></i>
                        用户
                    </button>
                    <button onclick="showModule('orders')" id="ordersNavTraffic" class="sidebar-item w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md">
                        <i data-lucide="shopping-cart" class="w-5 h-5 mr-3"></i>
                        订单
                    </button>
                </div>
            </nav>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 overflow-auto">
            <div class="p-6">
                <!-- 面包屑导航 -->
                <nav class="flex mb-4" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <button onclick="backToAdmin()" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                                <i data-lucide="users" class="w-4 h-4 mr-2"></i>
                                用户管理
                            </button>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i data-lucide="chevron-right" class="w-4 h-4 text-gray-400"></i>
                                <button onclick="backToAppsList()" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">应用列表</button>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i data-lucide="chevron-right" class="w-4 h-4 text-gray-400"></i>
                                <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">流量使用详情</span>
                            </div>
                        </li>
                    </ol>
                </nav>

                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2" id="trafficDetailsTitle">流量使用详情</h2>
                    <p class="text-gray-600" id="trafficDetailsSubtitle">应用：测试应用1 (app_123)</p>
                </div>

                <!-- 流量使用详情列表 -->
                <div class="bg-white shadow rounded-lg overflow-hidden">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布账号</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布内容</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">使用流量</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="trafficDetailsTableBody">
                            <!-- 动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建订单弹框 -->
    <div id="createOrderModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">创建订单</h3>
                    <button onclick="hideCreateOrderModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">手机号</label>
                        <input type="tel" id="orderPhone" placeholder="请输入手机号"
                               onblur="searchUserByPhone()"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <div id="userInfo" class="hidden mt-2 p-2 bg-gray-50 rounded text-sm">
                            <div>用户：<span id="userPhoneDisplay"></span></div>
                            <div>蚁贝余额：<span id="userBalance" class="font-medium text-blue-600"></span></div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">选择应用</label>
                        <select id="orderApp" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">请选择应用</option>
                            <option value="app_123">测试应用1 (app_123)</option>
                            <option value="app_456">测试应用2 (app_456)</option>
                            <option value="app_789">测试应用3 (app_789)</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">蚁贝数量</label>
                        <input type="number" id="orderAmount" placeholder="请输入蚁贝数量" min="1"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">充值类型</label>
                        <select id="orderType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="purchase">购买</option>
                            <option value="gift">赠送</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                        <textarea id="orderRemark" placeholder="请输入备注信息" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                    </div>

                    <div class="flex space-x-2 pt-4">
                        <button onclick="createOrder()" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md">
                            创建订单
                        </button>
                        <button onclick="hideCreateOrderModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 订单详情弹框 -->
    <div id="orderDetailModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">订单详情</h3>
                    <button onclick="hideOrderDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>

                <div class="space-y-3" id="orderDetailContent">
                    <!-- 动态填充订单详情 -->
                </div>

                <div class="flex justify-end mt-6">
                    <button onclick="hideOrderDetailModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化Lucide图标
        lucide.createIcons();

        // 模拟数据
        const mockUsers = {
            '***********': {
                phone: '***********',
                registerTime: '2024-01-15 10:30:25',
                appCount: 2,
                remark: 'VIP用户',
                balance: '1250.50',
                apps: [
                    { name: '测试应用1', id: 'app_123', balance: '850.25', accounts: 150, traffic: '2.5GB' },
                    { name: '测试应用2', id: 'app_456', balance: '400.25', accounts: 75, traffic: '1.2GB' }
                ]
            },
            '***********': {
                phone: '***********',
                registerTime: '2024-01-14 15:20:10',
                appCount: 1,
                remark: '',
                balance: '500.00',
                apps: [
                    { name: '测试应用3', id: 'app_789', balance: '500.00', accounts: 50, traffic: '0.8GB' }
                ]
            },
            '***********': {
                phone: '***********',
                registerTime: '2024-01-13 09:45:30',
                appCount: 3,
                remark: '测试账户',
                balance: '2100.75',
                apps: [
                    { name: '测试应用A', id: 'app_aaa', balance: '700.25', accounts: 100, traffic: '1.8GB' },
                    { name: '测试应用B', id: 'app_bbb', balance: '800.50', accounts: 120, traffic: '2.1GB' },
                    { name: '测试应用C', id: 'app_ccc', balance: '600.00', accounts: 80, traffic: '1.5GB' }
                ]
            }
        };

        const mockOrders = [
            {
                id: 'ORD202401150001',
                phone: '***********',
                source: '系统订单',
                amount: '100 蚁贝',
                payment: '¥100.00',
                app: '测试应用1 (app_123)',
                paymentMethod: '微信支付',
                status: '已完成',
                type: '购买',
                completeTime: '2024-01-15 14:30:25',
                remark: '正常购买订单'
            },
            {
                id: 'ORD202401140002',
                phone: '***********',
                source: '线上订单',
                amount: '50 蚁贝',
                payment: '¥0.00',
                app: '测试应用2 (app_456)',
                paymentMethod: '-',
                status: '已完成',
                type: '赠送',
                completeTime: '2024-01-14 16:20:10',
                remark: '新用户赠送'
            }
        ];

        // API调用记录模拟数据
        const mockApiCalls = [
            {
                time: '2024-01-15 14:30:25',
                endpoint: '/api/v1/content/publish',
                method: 'POST',
                status: 200,
                cost: '2.5',
                responseTime: '245ms',
                ip: '*************',
                capability: '一键发布',
                capabilityType: 'publish'
            },
            {
                time: '2024-01-15 14:28:15',
                endpoint: '/api/v1/account/auth',
                method: 'POST',
                status: 200,
                cost: '1.0',
                responseTime: '120ms',
                ip: '*************',
                capability: '账号授权',
                capabilityType: 'auth'
            },
            {
                time: '2024-01-15 14:25:30',
                endpoint: '/api/v1/message/private',
                method: 'GET',
                status: 200,
                cost: '0.8',
                responseTime: '180ms',
                ip: '*************',
                capability: '获取私信',
                capabilityType: 'message'
            },
            {
                time: '2024-01-15 14:20:45',
                endpoint: '/api/v1/message/reply',
                method: 'POST',
                status: 200,
                cost: '0.5',
                responseTime: '150ms',
                ip: '*************',
                capability: '回复私信',
                capabilityType: 'reply'
            },
            {
                time: '2024-01-15 13:45:20',
                endpoint: '/api/v1/content/upload',
                method: 'POST',
                status: 200,
                cost: '3.0',
                responseTime: '1.2s',
                ip: '*************',
                capability: '一键发布',
                capabilityType: 'publish'
            },
            {
                time: '2024-01-15 13:30:10',
                endpoint: '/api/v1/account/info',
                method: 'GET',
                status: 200,
                cost: '0.3',
                responseTime: '95ms',
                ip: '*************',
                capability: '账号授权',
                capabilityType: 'auth'
            },
            {
                time: '2024-01-15 12:15:35',
                endpoint: '/api/v1/analytics/data',
                method: 'GET',
                status: 200,
                cost: '0.6',
                responseTime: '200ms',
                ip: '*************',
                capability: '数据分析',
                capabilityType: 'analytics'
            },
            {
                time: '2024-01-15 11:50:18',
                endpoint: '/api/v1/message/send',
                method: 'POST',
                status: 200,
                cost: '0.4',
                responseTime: '130ms',
                ip: '*************',
                capability: '回复私信',
                capabilityType: 'reply'
            }
        ];

        // 流量使用详情模拟数据
        const mockTrafficDetails = [
            {
                account: 'user_xiaoming',
                accountName: '小明的生活记录',
                avatar: 'https://via.placeholder.com/40x40/4F46E5/FFFFFF?text=小明',
                platform: 'douyin',
                platformName: '抖音',
                platformLogo: 'https://via.placeholder.com/16x16/FF0050/FFFFFF?text=抖',
                cover: 'https://via.placeholder.com/60x60',
                title: '如何提高工作效率的10个小技巧',
                type: '视频',
                publishTime: '2024-01-15 14:30:25',
                traffic: '1.2GB'
            },
            {
                account: 'user_xiaohong',
                accountName: '小红的时尚日记',
                avatar: 'https://via.placeholder.com/40x40/EC4899/FFFFFF?text=小红',
                platform: 'xiaohongshu',
                platformName: '小红书',
                platformLogo: 'https://via.placeholder.com/16x16/FF2442/FFFFFF?text=红',
                cover: 'https://via.placeholder.com/60x60',
                title: '春季穿搭指南：时尚与舒适并存',
                type: '图文',
                publishTime: '2024-01-15 13:45:10',
                traffic: '0.8GB'
            },
            {
                account: 'user_xiaogang',
                accountName: '小刚的健康生活',
                avatar: 'https://via.placeholder.com/40x40/10B981/FFFFFF?text=小刚',
                platform: 'weixin',
                platformName: '微信公众号',
                platformLogo: 'https://via.placeholder.com/16x16/07C160/FFFFFF?text=微',
                cover: 'https://via.placeholder.com/60x60',
                title: '健康饮食：营养搭配的重要性',
                type: '文章',
                publishTime: '2024-01-15 12:20:30',
                traffic: '0.3GB'
            },
            {
                account: 'user_xiaoli',
                accountName: '小丽的旅行世界',
                avatar: 'https://via.placeholder.com/40x40/F59E0B/FFFFFF?text=小丽',
                platform: 'bilibili',
                platformName: 'B站',
                platformLogo: 'https://via.placeholder.com/16x16/00A1D6/FFFFFF?text=B',
                cover: 'https://via.placeholder.com/60x60',
                title: '旅行攻略：探索未知的美丽风景',
                type: '视频',
                publishTime: '2024-01-15 11:15:45',
                traffic: '2.1GB'
            }
        ];

        // 登录功能
        function login() {
            const phone = document.getElementById('loginPhone').value;
            const password = document.getElementById('loginPassword').value;

            if (!phone || !password) {
                alert('请输入手机号和密码');
                return;
            }

            // 模拟登录验证
            if (phone === '13800138000' && password === '123456') {
                document.getElementById('loginPage').classList.add('hidden');
                document.getElementById('adminPage').classList.remove('hidden');
            } else {
                alert('手机号或密码错误\n提示：手机号 13800138000，密码 123456');
            }
        }

        // 模块切换
        function showModule(module) {
            // 如果当前在其他页面，先返回管理后台
            if (!document.getElementById('appsListPage').classList.contains('hidden') ||
                !document.getElementById('apiCallsPage').classList.contains('hidden') ||
                !document.getElementById('trafficDetailsPage').classList.contains('hidden')) {
                backToAdmin();
            }

            // 隐藏所有模块
            document.getElementById('usersModule').classList.add('hidden');
            document.getElementById('ordersModule').classList.add('hidden');

            // 重置导航样式（管理后台页面）
            document.getElementById('usersNav').className = 'sidebar-item w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md';
            document.getElementById('ordersNav').className = 'sidebar-item w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md';

            // 重置导航样式（应用列表页面）
            const usersNavApps = document.getElementById('usersNavApps');
            const ordersNavApps = document.getElementById('ordersNavApps');
            const usersNavApi = document.getElementById('usersNavApi');
            const ordersNavApi = document.getElementById('ordersNavApi');
            const usersNavTraffic = document.getElementById('usersNavTraffic');
            const ordersNavTraffic = document.getElementById('ordersNavTraffic');

            [usersNavApps, ordersNavApps, usersNavApi, ordersNavApi, usersNavTraffic, ordersNavTraffic].forEach(nav => {
                if (nav) {
                    nav.className = 'sidebar-item w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md';
                }
            });

            // 显示选中模块
            if (module === 'users') {
                document.getElementById('usersModule').classList.remove('hidden');
                document.getElementById('usersNav').className = 'sidebar-item active w-full flex items-center px-3 py-2 text-sm font-medium rounded-md';
                [usersNavApps, usersNavApi, usersNavTraffic].forEach(nav => {
                    if (nav) {
                        nav.className = 'sidebar-item active w-full flex items-center px-3 py-2 text-sm font-medium rounded-md';
                    }
                });
            } else if (module === 'orders') {
                document.getElementById('ordersModule').classList.remove('hidden');
                document.getElementById('ordersNav').className = 'sidebar-item active w-full flex items-center px-3 py-2 text-sm font-medium rounded-md';
                [ordersNavApps, ordersNavApi, ordersNavTraffic].forEach(nav => {
                    if (nav) {
                        nav.className = 'sidebar-item active w-full flex items-center px-3 py-2 text-sm font-medium rounded-md';
                    }
                });
            }
        }

        // 搜索用户
        function searchUsers() {
            const phone = document.getElementById('phoneSearch').value;
            const date = document.getElementById('dateFilter').value;

            if (phone || date) {
                alert(`搜索功能演示\n手机号: ${phone}\n日期: ${date}`);
            } else {
                alert('请输入搜索条件');
            }
        }

        // 显示用户应用列表页面
        function showUserApps(phone) {
            const user = mockUsers[phone];
            if (!user) {
                alert('用户不存在');
                return;
            }

            // 隐藏管理后台页面
            document.getElementById('adminPage').classList.add('hidden');

            // 显示应用列表页面
            document.getElementById('appsListPage').classList.remove('hidden');

            // 更新页面标题
            document.getElementById('appsListTitle').textContent = `用户应用列表`;
            document.getElementById('appsListSubtitle').textContent = `用户：${phone}`;

            // 填充应用列表数据
            const tbody = document.getElementById('appsListTableBody');
            tbody.innerHTML = '';

            user.apps.forEach(app => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${app.name}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${app.id}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <button onclick="showApiCalls('${app.id}', '${app.name}')" class="text-blue-600 hover:text-blue-900 font-medium">
                            ${app.balance} 蚁贝
                        </button>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${app.accounts}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <button onclick="showTrafficDetails('${app.id}', '${app.name}')" class="text-blue-600 hover:text-blue-900 font-medium">
                            ${app.traffic}
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // 重新初始化图标
            lucide.createIcons();
        }

        // 返回管理后台
        function backToAdmin() {
            document.getElementById('appsListPage').classList.add('hidden');
            document.getElementById('apiCallsPage').classList.add('hidden');
            document.getElementById('trafficDetailsPage').classList.add('hidden');
            document.getElementById('adminPage').classList.remove('hidden');
        }

        // 返回应用列表
        function backToAppsList() {
            document.getElementById('apiCallsPage').classList.add('hidden');
            document.getElementById('trafficDetailsPage').classList.add('hidden');
            document.getElementById('appsListPage').classList.remove('hidden');
        }

        // 显示API调用记录页面
        function showApiCalls(appId, appName) {
            // 隐藏应用列表页面
            document.getElementById('appsListPage').classList.add('hidden');

            // 显示API调用记录页面
            document.getElementById('apiCallsPage').classList.remove('hidden');

            // 更新页面标题
            document.getElementById('apiCallsTitle').textContent = 'API调用记录';
            document.getElementById('apiCallsSubtitle').textContent = `应用：${appName} (${appId})`;

            // 重置筛选条件
            document.getElementById('capabilityFilter').value = '';
            document.getElementById('startTimeFilter').value = '';
            document.getElementById('endTimeFilter').value = '';

            // 填充API调用记录数据
            renderApiCalls(mockApiCalls);

            // 重新初始化图标
            lucide.createIcons();
        }

        // 渲染API调用记录表格
        function renderApiCalls(calls) {
            const tbody = document.getElementById('apiCallsTableBody');
            tbody.innerHTML = '';

            calls.forEach(call => {
                const row = document.createElement('tr');

                // 根据能力类型设置不同的颜色
                const capabilityColors = {
                    'auth': 'bg-purple-100 text-purple-800',
                    'publish': 'bg-blue-100 text-blue-800',
                    'message': 'bg-green-100 text-green-800',
                    'reply': 'bg-yellow-100 text-yellow-800',
                    'analytics': 'bg-indigo-100 text-indigo-800'
                };

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${call.time}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${capabilityColors[call.capabilityType] || 'bg-gray-100 text-gray-800'}">${call.capability}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">${call.endpoint}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${call.method === 'POST' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}">${call.method}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">${call.status}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">-${call.cost} 蚁贝</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${call.responseTime}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${call.ip}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // 显示流量使用详情页面
        function showTrafficDetails(appId, appName) {
            // 隐藏应用列表页面
            document.getElementById('appsListPage').classList.add('hidden');

            // 显示流量使用详情页面
            document.getElementById('trafficDetailsPage').classList.remove('hidden');

            // 更新页面标题
            document.getElementById('trafficDetailsTitle').textContent = '流量使用详情';
            document.getElementById('trafficDetailsSubtitle').textContent = `应用：${appName} (${appId})`;

            // 填充流量使用详情数据
            const tbody = document.getElementById('trafficDetailsTableBody');
            tbody.innerHTML = '';

            mockTrafficDetails.forEach(detail => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="relative">
                                <img class="h-10 w-10 rounded-full object-cover" src="${detail.avatar}" alt="${detail.accountName}">
                                <img class="absolute -bottom-1 -right-1 h-4 w-4 rounded-full border-2 border-white" src="${detail.platformLogo}" alt="${detail.platformName}" title="${detail.platformName}">
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">${detail.accountName}</div>
                                <div class="text-sm text-gray-500">${detail.account}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <img class="h-10 w-10 rounded object-cover mr-3" src="${detail.cover}" alt="封面">
                            <div>
                                <div class="text-sm font-medium text-gray-900">${detail.title}</div>
                                <div class="text-sm text-gray-500">${detail.publishTime}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            detail.type === '视频' ? 'bg-red-100 text-red-800' :
                            detail.type === '图文' ? 'bg-blue-100 text-blue-800' :
                            'bg-green-100 text-green-800'
                        }">${detail.type}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${detail.publishTime}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-orange-600">${detail.traffic}</td>
                `;
                tbody.appendChild(row);
            });

            // 重新初始化图标
            lucide.createIcons();
        }

        // 筛选API调用记录
        function filterApiCalls() {
            const capabilityFilter = document.getElementById('capabilityFilter').value;
            const startTimeFilter = document.getElementById('startTimeFilter').value;
            const endTimeFilter = document.getElementById('endTimeFilter').value;

            let filteredCalls = mockApiCalls;

            // 按能力类型筛选
            if (capabilityFilter) {
                filteredCalls = filteredCalls.filter(call => call.capabilityType === capabilityFilter);
            }

            // 按时间范围筛选
            if (startTimeFilter) {
                const startTime = new Date(startTimeFilter);
                filteredCalls = filteredCalls.filter(call => new Date(call.time) >= startTime);
            }

            if (endTimeFilter) {
                const endTime = new Date(endTimeFilter);
                filteredCalls = filteredCalls.filter(call => new Date(call.time) <= endTime);
            }

            // 重新渲染表格
            renderApiCalls(filteredCalls);

            // 显示筛选结果提示
            const totalCount = mockApiCalls.length;
            const filteredCount = filteredCalls.length;
            if (filteredCount < totalCount) {
                // 可以在这里添加筛选结果提示
                console.log(`筛选结果：${filteredCount}/${totalCount} 条记录`);
            }
        }

        // 重置API调用记录筛选
        function resetApiFilters() {
            document.getElementById('capabilityFilter').value = '';
            document.getElementById('startTimeFilter').value = '';
            document.getElementById('endTimeFilter').value = '';

            // 重新渲染所有数据
            renderApiCalls(mockApiCalls);
        }

        // 创建订单相关
        function showCreateOrderModal() {
            document.getElementById('createOrderModal').classList.remove('hidden');
            // 清空表单
            document.getElementById('orderPhone').value = '';
            document.getElementById('orderApp').value = '';
            document.getElementById('orderAmount').value = '';
            document.getElementById('orderType').value = 'purchase';
            document.getElementById('orderRemark').value = '';
            document.getElementById('userInfo').classList.add('hidden');
        }

        function hideCreateOrderModal() {
            document.getElementById('createOrderModal').classList.add('hidden');
        }

        function searchUserByPhone() {
            const phone = document.getElementById('orderPhone').value;
            const user = mockUsers[phone];

            if (user) {
                document.getElementById('userPhoneDisplay').textContent = phone;
                document.getElementById('userBalance').textContent = user.balance + ' 蚁贝';
                document.getElementById('userInfo').classList.remove('hidden');
            } else {
                document.getElementById('userInfo').classList.add('hidden');
            }
        }

        function createOrder() {
            const phone = document.getElementById('orderPhone').value;
            const app = document.getElementById('orderApp').value;
            const amount = document.getElementById('orderAmount').value;
            const type = document.getElementById('orderType').value;
            const remark = document.getElementById('orderRemark').value;

            if (!phone || !app || !amount) {
                alert('请填写完整的订单信息');
                return;
            }

            const orderId = 'ORD' + Date.now();
            alert(`订单创建成功！\n订单号: ${orderId}\n手机号: ${phone}\n应用: ${app}\n数量: ${amount} 蚁贝\n类型: ${type === 'purchase' ? '购买' : '赠送'}\n备注: ${remark}`);

            hideCreateOrderModal();
        }

        // 订单详情
        function showOrderDetail(orderId) {
            const order = mockOrders.find(o => o.id === orderId);
            if (!order) {
                alert('订单不存在');
                return;
            }

            const content = document.getElementById('orderDetailContent');
            content.innerHTML = `
                <div class="border-b pb-2 mb-2">
                    <div class="text-sm text-gray-500">订单号</div>
                    <div class="font-medium">${order.id}</div>
                </div>
                <div class="border-b pb-2 mb-2">
                    <div class="text-sm text-gray-500">手机号</div>
                    <div class="font-medium">${order.phone}</div>
                </div>
                <div class="border-b pb-2 mb-2">
                    <div class="text-sm text-gray-500">订单来源</div>
                    <div class="font-medium">${order.source}</div>
                </div>
                <div class="border-b pb-2 mb-2">
                    <div class="text-sm text-gray-500">购买数量</div>
                    <div class="font-medium">${order.amount}</div>
                </div>
                <div class="border-b pb-2 mb-2">
                    <div class="text-sm text-gray-500">付款金额</div>
                    <div class="font-medium">${order.payment}</div>
                </div>
                <div class="border-b pb-2 mb-2">
                    <div class="text-sm text-gray-500">应用信息</div>
                    <div class="font-medium">${order.app}</div>
                </div>
                <div class="border-b pb-2 mb-2">
                    <div class="text-sm text-gray-500">付款方式</div>
                    <div class="font-medium">${order.paymentMethod}</div>
                </div>
                <div class="border-b pb-2 mb-2">
                    <div class="text-sm text-gray-500">订单状态</div>
                    <div class="font-medium">${order.status}</div>
                </div>
                <div class="border-b pb-2 mb-2">
                    <div class="text-sm text-gray-500">订单类型</div>
                    <div class="font-medium">${order.type}</div>
                </div>
                <div class="border-b pb-2 mb-2">
                    <div class="text-sm text-gray-500">完成时间</div>
                    <div class="font-medium">${order.completeTime}</div>
                </div>
                <div>
                    <div class="text-sm text-gray-500">备注</div>
                    <div class="font-medium">${order.remark}</div>
                </div>
            `;

            document.getElementById('orderDetailModal').classList.remove('hidden');
        }

        function hideOrderDetailModal() {
            document.getElementById('orderDetailModal').classList.add('hidden');
        }

        // 点击外部关闭弹框
        document.addEventListener('click', (e) => {
            const modals = ['createOrderModal', 'orderDetailModal'];
            modals.forEach(modalId => {
                const modal = document.getElementById(modalId);
                if (e.target === modal) {
                    modal.classList.add('hidden');
                }
            });
        });

        // 回车登录
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !document.getElementById('loginPage').classList.contains('hidden')) {
                login();
            }
        });
    </script>
</body>
</html>
