<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蚁小二开放平台管理后台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.css" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .slider-track {
            position: relative;
            height: 40px;
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            overflow: hidden;
            user-select: none;
        }
        .slider-progress {
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            background: #dbeafe;
            transition: width 0.2s;
        }
        .slider-button {
            position: absolute;
            top: 0;
            width: 40px;
            height: 40px;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }
        .slider-button:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transform: scale(1.05);
        }
        .slider-text {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #6b7280;
            pointer-events: none;
        }
        .verified {
            background: #dcfce7 !important;
            border-color: #16a34a !important;
        }
        .verified .slider-button {
            background: #16a34a !important;
            border-color: #16a34a !important;
            color: white !important;
        }
        .verified .slider-text {
            color: #16a34a !important;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 登录页面 -->
    <div id="loginPage" class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900">登录账户</h2>
                <p class="mt-2 text-sm text-gray-600">请选择登录方式</p>
            </div>
            
            <!-- 登录方式切换 -->
            <div class="bg-gray-100 p-1 rounded-lg flex">
                <button id="phoneTab" class="flex-1 py-2 px-4 text-sm font-medium rounded-md bg-white text-gray-900 shadow-sm">验证码登录</button>
                <button id="passwordTab" class="flex-1 py-2 px-4 text-sm font-medium rounded-md text-gray-500 hover:text-gray-700">密码登录</button>
            </div>

            <!-- 验证码登录表单 -->
            <div id="phoneForm" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700">手机号码</label>
                    <input type="tel" placeholder="请输入您的手机号码" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">验证码</label>
                    <div class="mt-1 flex space-x-2">
                        <input type="text" placeholder="请输入验证码" class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">获取验证码</button>
                    </div>
                </div>
                <button onclick="login()" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    登录
                </button>
            </div>

            <!-- 密码登录表单 -->
            <div id="passwordForm" class="space-y-6 hidden">
                <div>
                    <label class="block text-sm font-medium text-gray-700">手机号码</label>
                    <input type="tel" placeholder="请输入您的手机号码" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">密码</label>
                    <input type="password" placeholder="请输入密码" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">滑块验证</label>
                    <div class="mt-1 slider-track" id="sliderCaptcha">
                        <div class="slider-progress" id="sliderProgress"></div>
                        <div class="slider-text" id="sliderText">向右滑动完成验证</div>
                        <div class="slider-button" id="sliderButton">
                            <i data-lucide="arrow-right" class="w-4 h-4"></i>
                        </div>
                    </div>
                </div>
                <button onclick="login()" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    登录
                </button>
            </div>

            <div class="text-center text-sm text-gray-500">
                <p>登录即表示您同意我们的 <a href="#" class="text-blue-600 hover:underline">服务条款</a> 和 <a href="#" class="text-blue-600 hover:underline">隐私政策</a></p>
            </div>
        </div>
    </div>

    <!-- 主页面 -->
    <div id="mainPage" class="hidden min-h-screen bg-gray-50">
        <!-- 顶部导航 -->
        <nav class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center space-x-8">
                        <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">张</div>
                            <span class="text-xl font-semibold text-gray-900">控制台</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <button onclick="toggleDropdown()" class="h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">张</button>
                            <div id="dropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">订单管理</a>
                                <a href="#" onclick="logout()" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 主要内容 -->
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">蚁小二开放平台管理后台</h1>
                    <p class="text-gray-600 mt-1">欢迎回来！</p>
                </div>
                <button onclick="showCreateAppModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    创建应用
                </button>
            </div>

            <!-- 我的应用 -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                <i data-lucide="smartphone" class="w-5 h-5 mr-2"></i>
                                我的应用
                            </h3>
                            <p class="text-sm text-gray-500">管理您的应用</p>
                        </div>
                        <button onclick="showCreateAppModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                            创建应用
                        </button>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">应用名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">应用ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">蚁贝</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">账号</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">流量</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="smartphone" class="w-4 h-4 text-blue-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">测试应用1</div>
                                            <div class="text-sm text-gray-500">这是一个测试应用</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <code class="text-sm bg-gray-100 px-2 py-1 rounded">app_123456789</code>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="text-sm font-medium text-blue-600">1000.50 蚁贝</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="text-sm font-medium">150</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="text-sm font-medium">2.5GB</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button onclick="showAppDetail('1')" class="text-blue-600 hover:text-blue-900 mr-3">查看详情</button>
                                    <button class="text-gray-400 hover:text-gray-600 mr-3">
                                        <i data-lucide="edit" class="w-4 h-4"></i>
                                    </button>
                                    <button class="text-red-400 hover:text-red-600">
                                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <i data-lucide="smartphone" class="w-4 h-4 text-blue-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">测试应用2</div>
                                            <div class="text-sm text-gray-500">另一个测试应用</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <code class="text-sm bg-gray-100 px-2 py-1 rounded">app_987654321</code>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="text-sm font-medium text-blue-600">500.25 蚁贝</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="text-sm font-medium">75</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="text-sm font-medium">1.2GB</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button onclick="showAppDetail('2')" class="text-blue-600 hover:text-blue-900 mr-3">查看详情</button>
                                    <button class="text-gray-400 hover:text-gray-600 mr-3">
                                        <i data-lucide="edit" class="w-4 h-4"></i>
                                    </button>
                                    <button class="text-red-400 hover:text-red-600">
                                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 应用详情页面 -->
    <div id="appDetailPage" class="hidden min-h-screen bg-gray-50 flex">
        <!-- 左侧菜单栏 -->
        <div class="w-64 bg-white border-r border-gray-200 flex flex-col">
            <!-- 头像和控制台 -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="h-10 w-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">张</div>
                    <div>
                        <div class="font-semibold">控制台</div>
                        <div class="text-sm text-gray-500" id="appNameInSidebar">测试应用1</div>
                    </div>
                </div>
            </div>

            <!-- 菜单项 -->
            <nav class="flex-1 p-4">
                <div class="space-y-2">
                    <button onclick="showTab('overview')" id="overviewTab" class="w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left bg-blue-600 text-white">
                        <i data-lucide="bar-chart-3" class="w-4 h-4"></i>
                        <span>概览</span>
                    </button>
                    <button onclick="showTab('capabilities')" id="capabilitiesTab" class="w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left text-gray-700 hover:bg-gray-100">
                        <i data-lucide="zap" class="w-4 h-4"></i>
                        <span>能力</span>
                    </button>
                    <button onclick="showTab('call-details')" id="callDetailsTab" class="w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left text-gray-700 hover:bg-gray-100">
                        <i data-lucide="phone" class="w-4 h-4"></i>
                        <span>调用明细</span>
                    </button>
                </div>
            </nav>

            <!-- 返回首页 -->
            <div class="p-4 border-t border-gray-200">
                <button onclick="showMainPage()" class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <i data-lucide="home" class="w-4 h-4 mr-2"></i>
                    返回首页
                </button>
            </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="flex-1 overflow-auto">
            <div class="p-6">
                <!-- 页面标题 -->
                <div class="flex items-center justify-between mb-6">
                    <h1 class="text-2xl font-bold text-gray-900" id="appDetailTitle">测试应用1</h1>
                    <div class="flex space-x-2">
                        <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <i data-lucide="credit-card" class="w-4 h-4 mr-2"></i>
                            充值
                        </button>
                        <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <i data-lucide="settings" class="w-4 h-4 mr-2"></i>
                            设置
                        </button>
                    </div>
                </div>

                <!-- 概览内容 -->
                <div id="overviewContent" class="space-y-6">
                    <!-- 应用信息卡片 -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">应用信息</h3>
                        </div>
                        <div class="p-6 space-y-4">
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-medium text-gray-500">应用名称：</span>
                                <span class="font-medium">测试应用1</span>
                                <span class="text-sm text-gray-500">（这是一个测试应用）</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-medium text-gray-500">appID：</span>
                                <code class="text-sm bg-gray-100 px-2 py-1 rounded border">app_123456789</code>
                                <button onclick="copyToClipboard('app_123456789')" class="p-1 text-gray-400 hover:text-gray-600">
                                    <i data-lucide="copy" class="w-3 h-3"></i>
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-medium text-gray-500">Secret：</span>
                                <code class="text-sm bg-gray-100 px-2 py-1 rounded border">••••••••••••••••••••••••••••••••</code>
                                <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">
                                    <i data-lucide="refresh-cw" class="w-3 h-3 mr-1"></i>
                                    重新生成
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 数据统计卡片 -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">数据统计</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <!-- 蚁贝余额 -->
                                <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 border border-blue-200">
                                    <div class="flex items-center justify-between">
                                        <div class="space-y-1">
                                            <div class="text-2xl font-bold text-blue-700">1000.50</div>
                                            <div class="text-sm text-blue-600 flex items-center">
                                                <i data-lucide="coins" class="w-4 h-4 mr-1"></i>
                                                蚁贝余额
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 账号统计 -->
                                <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6 border border-green-200">
                                    <div class="flex items-center justify-between">
                                        <div class="space-y-1">
                                            <div class="text-2xl font-bold text-green-700">150</div>
                                            <div class="text-sm text-green-600 flex items-center">
                                                <i data-lucide="users" class="w-4 h-4 mr-1"></i>
                                                账号数量
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 流量统计 -->
                                <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 border border-yellow-200">
                                    <div class="flex items-center justify-between">
                                        <div class="space-y-1">
                                            <div class="text-2xl font-bold text-yellow-700">2.5G</div>
                                            <div class="text-sm text-yellow-600 flex items-center">
                                                <i data-lucide="zap" class="w-4 h-4 mr-1"></i>
                                                流量使用
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 接口调用统计 -->
                                <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6 border border-purple-200">
                                    <div class="flex items-center justify-between">
                                        <div class="space-y-1">
                                            <div class="text-2xl font-bold text-purple-700">1.25k</div>
                                            <div class="text-sm text-purple-600 flex items-center">
                                                <i data-lucide="activity" class="w-4 h-4 mr-1"></i>
                                                接口调用
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 30日趋势图 -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">30日趋势</h3>
                            <p class="text-sm text-gray-500">最近30天的使用趋势分析</p>
                        </div>
                        <div class="p-6">
                            <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                                <div class="text-center">
                                    <i data-lucide="bar-chart-3" class="w-12 h-12 text-gray-400 mx-auto mb-4"></i>
                                    <p class="text-gray-500">30日数据曲线图</p>
                                    <p class="text-sm text-gray-400">（静态页面展示）</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 能力内容 -->
                <div id="capabilitiesContent" class="hidden space-y-6">
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">API 能力</h3>
                            <p class="text-sm text-gray-500">查看可用的API接口和功能</p>
                        </div>
                        <div class="p-6">
                            <div class="text-center py-8">
                                <i data-lucide="zap" class="w-12 h-12 text-gray-400 mx-auto mb-4"></i>
                                <p class="text-gray-500">API能力模块正在开发中</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 调用明细内容 -->
                <div id="callDetailsContent" class="hidden space-y-6">
                    <!-- 余额信息 -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                <i data-lucide="coins" class="w-5 h-5 mr-2"></i>
                                蚁贝余额
                            </h3>
                            <p class="text-sm text-gray-500">蚁贝是平台虚拟货币，1元 = 1蚁贝</p>
                        </div>
                        <div class="p-6">
                            <div class="text-3xl font-bold text-blue-600 mb-4">1000.50 蚁贝</div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-4">
                                <div><span class="font-medium">账号额度：</span>1个额度 = 40蚁贝</div>
                                <div><span class="font-medium">流量：</span>1GB = 1蚁贝</div>
                            </div>
                            <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                <i data-lucide="credit-card" class="w-4 h-4 mr-2"></i>
                                立即充值
                            </button>
                        </div>
                    </div>

                    <!-- 交易记录和API调用记录 -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="border-b border-gray-200">
                            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                                <button onclick="showCallDetailsTab('transactions')" id="transactionsTabBtn" class="border-b-2 border-blue-500 py-4 px-1 text-sm font-medium text-blue-600">
                                    交易记录
                                </button>
                                <button onclick="showCallDetailsTab('api-calls')" id="apiCallsTabBtn" class="border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                                    API调用记录
                                </button>
                            </nav>
                        </div>

                        <!-- 交易记录 -->
                        <div id="transactionsTab" class="p-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">交易流水</h4>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">类型</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">金额</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">交易前余额</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">交易后余额</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">描述</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">时间</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i data-lucide="trending-up" class="w-3 h-3 mr-1"></i>
                                                    充值
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">+100.00</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">900.50</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1000.50</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">充值</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 14:30:25</td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    <i data-lucide="trending-down" class="w-3 h-3 mr-1"></i>
                                                    消费
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">-10.00</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">910.50</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">900.50</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">API调用消费</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-14 10:15:30</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- API调用记录 -->
                        <div id="apiCallsTab" class="hidden p-6">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">API调用记录</h4>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">API端点</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">方法</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">扣费类型</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">扣费金额</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">状态码</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">调用时间</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono">/api/v1/accounts</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <div class="flex items-center">
                                                    <i data-lucide="shield" class="w-4 h-4 text-blue-500 mr-2"></i>
                                                    账号额度
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">-40.00 蚁贝</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">200</span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 14:25:10</td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono">/api/v1/traffic</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <div class="flex items-center">
                                                    <i data-lucide="zap" class="w-4 h-4 text-yellow-500 mr-2"></i>
                                                    流量
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">-1.50 蚁贝</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">200</span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 13:45:22</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建应用弹框 -->
    <div id="createAppModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">创建应用</h3>
                    <button onclick="hideCreateAppModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>
                <p class="text-sm text-gray-500 mb-4">输入应用名称来创建新应用，系统将自动生成应用ID和密钥</p>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            应用名称 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="appNameInput" placeholder="请输入应用名称（1-10字符）" maxlength="10"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <div class="flex justify-between text-sm text-gray-500 mt-1">
                            <span id="appNameError" class="text-red-500"></span>
                            <span id="appNameCount">0/10</span>
                        </div>
                    </div>

                    <div class="bg-gray-50 p-3 rounded-lg">
                        <h4 class="font-medium mb-2 text-sm">系统将自动生成：</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• 应用ID：用于标识您的应用程序</li>
                            <li>• 应用密钥：用于API调用认证</li>
                            <li>• 初始余额：0.00 蚁贝</li>
                        </ul>
                    </div>

                    <div class="flex space-x-2 pt-4">
                        <button onclick="createApp()" id="createAppBtn" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md">
                            创建应用
                        </button>
                        <button onclick="hideCreateAppModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建成功弹框 -->
    <div id="createSuccessModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-green-600">应用创建成功！</h3>
                    <button onclick="hideCreateSuccessModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>
                <p class="text-sm text-gray-500 mb-4">您的应用已成功创建，请妥善保存以下信息</p>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">应用名称</label>
                        <div class="mt-1 p-3 bg-gray-50 rounded-md" id="createdAppName">测试应用</div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">应用ID</label>
                        <div class="mt-1 flex items-center space-x-2">
                            <code class="flex-1 p-3 bg-gray-50 rounded-md font-mono text-sm" id="createdAppId">app_123456789</code>
                            <button onclick="copyToClipboard(document.getElementById('createdAppId').textContent)" class="p-2 text-gray-400 hover:text-gray-600">
                                <i data-lucide="copy" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">应用密钥</label>
                        <div class="mt-1 flex items-center space-x-2">
                            <code class="flex-1 p-3 bg-gray-50 rounded-md font-mono text-sm" id="createdAppSecret">sk_test_123456789abcdef</code>
                            <button onclick="toggleSecretVisibility()" id="toggleSecretBtn" class="p-2 text-gray-400 hover:text-gray-600">
                                <i data-lucide="eye-off" class="w-4 h-4"></i>
                            </button>
                            <button onclick="copyToClipboard(document.getElementById('createdAppSecret').textContent)" class="p-2 text-gray-400 hover:text-gray-600">
                                <i data-lucide="copy" class="w-4 h-4"></i>
                            </button>
                        </div>
                        <p class="text-sm text-gray-500 mt-1">请妥善保存您的密钥，它将用于API调用认证</p>
                    </div>

                    <div class="flex space-x-2 pt-4">
                        <button onclick="hideCreateSuccessModal()" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md">
                            完成
                        </button>
                        <button onclick="continueCreate()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            继续创建
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化Lucide图标
        lucide.createIcons();

        // 登录相关功能
        function switchLoginTab(tab) {
            const phoneTab = document.getElementById('phoneTab');
            const passwordTab = document.getElementById('passwordTab');
            const phoneForm = document.getElementById('phoneForm');
            const passwordForm = document.getElementById('passwordForm');

            if (tab === 'phone') {
                phoneTab.className = 'flex-1 py-2 px-4 text-sm font-medium rounded-md bg-white text-gray-900 shadow-sm';
                passwordTab.className = 'flex-1 py-2 px-4 text-sm font-medium rounded-md text-gray-500 hover:text-gray-700';
                phoneForm.classList.remove('hidden');
                passwordForm.classList.add('hidden');
            } else {
                phoneTab.className = 'flex-1 py-2 px-4 text-sm font-medium rounded-md text-gray-500 hover:text-gray-700';
                passwordTab.className = 'flex-1 py-2 px-4 text-sm font-medium rounded-md bg-white text-gray-900 shadow-sm';
                phoneForm.classList.add('hidden');
                passwordForm.classList.remove('hidden');
            }
        }

        document.getElementById('phoneTab').addEventListener('click', () => switchLoginTab('phone'));
        document.getElementById('passwordTab').addEventListener('click', () => switchLoginTab('password'));

        // 滑块验证功能
        let isDragging = false;
        let isVerified = false;
        const sliderCaptcha = document.getElementById('sliderCaptcha');
        const sliderButton = document.getElementById('sliderButton');
        const sliderProgress = document.getElementById('sliderProgress');
        const sliderText = document.getElementById('sliderText');

        sliderButton.addEventListener('mousedown', (e) => {
            if (isVerified) return;
            isDragging = true;
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging || isVerified) return;

            const rect = sliderCaptcha.getBoundingClientRect();
            const newPosition = Math.max(0, Math.min(e.clientX - rect.left - 20, rect.width - 40));

            sliderButton.style.left = newPosition + 'px';
            sliderProgress.style.width = (newPosition + 40) + 'px';

            if (newPosition > rect.width - 60) {
                isVerified = true;
                isDragging = false;
                sliderCaptcha.classList.add('verified');
                sliderText.textContent = '验证成功';
                sliderButton.innerHTML = '<i data-lucide="check" class="w-4 h-4"></i>';
                lucide.createIcons();
            }
        });

        document.addEventListener('mouseup', () => {
            if (!isVerified && isDragging) {
                sliderButton.style.left = '0px';
                sliderProgress.style.width = '40px';
            }
            isDragging = false;
        });

        function login() {
            alert('登录功能演示 - 跳转到主页面');
            document.getElementById('loginPage').classList.add('hidden');
            document.getElementById('mainPage').classList.remove('hidden');
        }

        function logout() {
            document.getElementById('mainPage').classList.add('hidden');
            document.getElementById('appDetailPage').classList.add('hidden');
            document.getElementById('loginPage').classList.remove('hidden');
            document.getElementById('dropdown').classList.add('hidden');
        }

        function toggleDropdown() {
            const dropdown = document.getElementById('dropdown');
            dropdown.classList.toggle('hidden');
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            const dropdown = document.getElementById('dropdown');
            if (!e.target.closest('.relative')) {
                dropdown.classList.add('hidden');
            }
        });

        function showMainPage() {
            document.getElementById('appDetailPage').classList.add('hidden');
            document.getElementById('mainPage').classList.remove('hidden');
        }

        function showAppDetail(appId) {
            document.getElementById('mainPage').classList.add('hidden');
            document.getElementById('appDetailPage').classList.remove('hidden');

            // 更新应用详情信息
            const appName = appId === '1' ? '测试应用1' : '测试应用2';
            document.getElementById('appDetailTitle').textContent = appName;
            document.getElementById('appNameInSidebar').textContent = appName;
        }

        function showTab(tab) {
            // 隐藏所有内容
            document.getElementById('overviewContent').classList.add('hidden');
            document.getElementById('capabilitiesContent').classList.add('hidden');
            document.getElementById('callDetailsContent').classList.add('hidden');

            // 重置所有标签样式
            document.getElementById('overviewTab').className = 'w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left text-gray-700 hover:bg-gray-100';
            document.getElementById('capabilitiesTab').className = 'w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left text-gray-700 hover:bg-gray-100';
            document.getElementById('callDetailsTab').className = 'w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left text-gray-700 hover:bg-gray-100';

            // 显示选中的内容和标签
            if (tab === 'overview') {
                document.getElementById('overviewContent').classList.remove('hidden');
                document.getElementById('overviewTab').className = 'w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left bg-blue-600 text-white';
            } else if (tab === 'capabilities') {
                document.getElementById('capabilitiesContent').classList.remove('hidden');
                document.getElementById('capabilitiesTab').className = 'w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left bg-blue-600 text-white';
            } else if (tab === 'call-details') {
                document.getElementById('callDetailsContent').classList.remove('hidden');
                document.getElementById('callDetailsTab').className = 'w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left bg-blue-600 text-white';
            }
        }

        function showCallDetailsTab(tab) {
            const transactionsTab = document.getElementById('transactionsTab');
            const apiCallsTab = document.getElementById('apiCallsTab');
            const transactionsBtn = document.getElementById('transactionsTabBtn');
            const apiCallsBtn = document.getElementById('apiCallsTabBtn');

            if (tab === 'transactions') {
                transactionsTab.classList.remove('hidden');
                apiCallsTab.classList.add('hidden');
                transactionsBtn.className = 'border-b-2 border-blue-500 py-4 px-1 text-sm font-medium text-blue-600';
                apiCallsBtn.className = 'border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700';
            } else {
                transactionsTab.classList.add('hidden');
                apiCallsTab.classList.remove('hidden');
                transactionsBtn.className = 'border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700';
                apiCallsBtn.className = 'border-b-2 border-blue-500 py-4 px-1 text-sm font-medium text-blue-600';
            }
        }

        function showCreateAppModal() {
            document.getElementById('createAppModal').classList.remove('hidden');
            document.getElementById('appNameInput').value = '';
            document.getElementById('appNameError').textContent = '';
            document.getElementById('appNameCount').textContent = '0/10';
        }

        function hideCreateAppModal() {
            document.getElementById('createAppModal').classList.add('hidden');
        }

        function hideCreateSuccessModal() {
            document.getElementById('createSuccessModal').classList.add('hidden');
        }

        function createApp() {
            const appName = document.getElementById('appNameInput').value.trim();
            const errorElement = document.getElementById('appNameError');

            if (!appName) {
                errorElement.textContent = '应用名称不能为空';
                return;
            }

            if (appName.length > 10) {
                errorElement.textContent = '应用名称不能超过10个字符';
                return;
            }

            // 模拟创建应用
            const appId = 'app_' + Date.now();
            const secret = 'sk_test_' + Date.now() + 'abcdef';

            document.getElementById('createdAppName').textContent = appName;
            document.getElementById('createdAppId').textContent = appId;
            document.getElementById('createdAppSecret').textContent = secret;

            hideCreateAppModal();
            document.getElementById('createSuccessModal').classList.remove('hidden');
        }

        function continueCreate() {
            hideCreateSuccessModal();
            showCreateAppModal();
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('已复制到剪贴板');
            });
        }

        let secretVisible = false;
        function toggleSecretVisibility() {
            const secretElement = document.getElementById('createdAppSecret');
            const toggleBtn = document.getElementById('toggleSecretBtn');

            if (secretVisible) {
                secretElement.textContent = '••••••••••••••••••••••••••••••••';
                toggleBtn.innerHTML = '<i data-lucide="eye-off" class="w-4 h-4"></i>';
            } else {
                secretElement.textContent = secretElement.getAttribute('data-secret') || 'sk_test_123456789abcdef';
                toggleBtn.innerHTML = '<i data-lucide="eye" class="w-4 h-4"></i>';
            }
            secretVisible = !secretVisible;
            lucide.createIcons();
        }

        // 应用名称输入计数
        document.getElementById('appNameInput').addEventListener('input', (e) => {
            const value = e.target.value;
            const count = value.length;
            document.getElementById('appNameCount').textContent = count + '/10';
            document.getElementById('appNameError').textContent = '';
        });

        // 点击外部关闭弹框
        document.addEventListener('click', (e) => {
            const createModal = document.getElementById('createAppModal');
            const successModal = document.getElementById('createSuccessModal');

            if (e.target === createModal) {
                hideCreateAppModal();
            }
            if (e.target === successModal) {
                hideCreateSuccessModal();
            }
        });
    </script>
</body>
</html>
