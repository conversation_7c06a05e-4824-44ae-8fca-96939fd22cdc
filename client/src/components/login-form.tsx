'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { useEffect, useState } from 'react'
import { trpc } from '../lib/trpc'
import { saveAuthToken } from '../lib/auth/auth'
import { useAuth } from '../lib/auth/auth-context'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from './ui/form'
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs'
import { SliderCaptcha } from './ui/slider-captcha'
import { toast } from 'sonner'

const phoneSchema = z
  .string()
  .min(1, '请输入手机号码')
  .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号码')

// 手机验证码登录表单验证 schema
const phoneLoginSchema = z.object({
  phone: phoneSchema,
  code: z.string().min(1, '请输入验证码').length(6, '验证码必须是6位'),
})
type PhoneLoginForm = z.infer<typeof phoneLoginSchema>

// 密码登录表单验证 schema
const passwordLoginSchema = z.object({
  phone: phoneSchema,
  password: z.string().min(1, '请输入密码'),
  captchaVerified: z.boolean().refine(val => val === true, '请完成滑块验证'),
})
type PasswordLoginForm = z.infer<typeof passwordLoginSchema>

export function LoginForm() {
  const [countdown, setCountdown] = useState(0)
  const [loginType, setLoginType] = useState<'phone' | 'password'>('phone')
  const { login } = useAuth()

  // 手机验证码登录表单
  const phoneForm = useForm<PhoneLoginForm>({
    resolver: zodResolver(phoneLoginSchema),
    defaultValues: {
      phone: '',
      code: '',
    },
  })

  // 密码登录表单
  const passwordForm = useForm<PasswordLoginForm>({
    resolver: zodResolver(passwordLoginSchema),
    defaultValues: {
      phone: '',
      password: '',
      captchaVerified: false,
    },
  })

  // hasSendCode
  const [hasSendCode, setHasSendCode] = useState(false)
  const phone = phoneForm.watch('phone')

  useEffect(() => {
    if (phoneSchema.safeParse(phone).success) {
      setHasSendCode(true)
    }
  }, [phone])

  // 手机验证码登录 mutation
  const phoneLoginMutation = trpc.auth.phoneLogin.useMutation({
    onSuccess: (data) => {
      saveAuthToken(data.token)
      login(data.token)
      toast.success('登录成功')
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  // 密码登录 mutation
  const passwordLoginMutation = trpc.auth.login.useMutation({
    onSuccess: (data) => {
      saveAuthToken(data.token)
      login(data.token)
      toast.success('登录成功')
    },
    onError: (error) => {
      toast.error(error.message)
      // 重置滑块验证
      passwordForm.setValue('captchaVerified', false)
    },
  })

  // 发送验证码 mutation
  const sendSmsMutation = trpc.auth.sendSms.useMutation({
    onSuccess: (data) => {
      toast.success('发送成功', {
        description: data.code,
      })
      if (data.code) {
        phoneForm.setValue('code', data.code)
      }
      // 开始倒计时
      setCountdown(60)
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  // 手机验证码登录提交处理
  const onPhoneSubmit = (values: PhoneLoginForm) => {
    phoneLoginMutation.mutate(values)
  }

  // 密码登录提交处理
  const onPasswordSubmit = (values: PasswordLoginForm) => {
    passwordLoginMutation.mutate({
      phone: values.phone,
      password: values.password,
    })
  }

  // 发送验证码处理
  const handleSendCode = () => {
    if (countdown === 0 && hasSendCode) {
      sendSmsMutation.mutate({ phone, type: 'login' })
    }
  }

  // 滑块验证回调
  const handleCaptchaVerify = (verified: boolean) => {
    passwordForm.setValue('captchaVerified', verified)
  }

  return (
    <div className="w-full space-y-6">
      <div className="flex flex-col items-center gap-2 text-center">
        <h1 className="text-2xl font-bold">登录账户</h1>
        <p className="text-muted-foreground text-sm text-balance">请选择登录方式</p>
      </div>

      <Tabs value={loginType} onValueChange={(value) => setLoginType(value as 'phone' | 'password')} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="phone">验证码登录</TabsTrigger>
          <TabsTrigger value="password">密码登录</TabsTrigger>
        </TabsList>

        {/* 验证码登录 */}
        <TabsContent value="phone" className="space-y-4">
          <Form {...phoneForm}>
            <form onSubmit={phoneForm.handleSubmit(onPhoneSubmit)} className="flex flex-col gap-4">
              <FormField
                control={phoneForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem className="grid gap-2">
                    <FormLabel>手机号码</FormLabel>
                    <FormControl>
                      <Input type="tel" placeholder="请输入您的手机号码" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={phoneForm.control}
                name="code"
                render={({ field }) => (
                  <FormItem className="grid gap-2">
                    <FormLabel>验证码</FormLabel>
                    <div className="flex gap-2">
                      <FormControl>
                        <Input type="text" placeholder="请输入验证码" className="flex-1" {...field} />
                      </FormControl>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleSendCode}
                        disabled={sendSmsMutation.isPending || countdown > 0 || !hasSendCode}
                        className="shrink-0 min-w-[100px]"
                      >
                        {sendSmsMutation.isPending ? (
                          <div className="flex items-center gap-1">
                            <div className="h-3 w-3 animate-spin rounded-full border border-current border-t-transparent" />
                            发送中
                          </div>
                        ) : countdown > 0 ? (
                          `${countdown}s后重发`
                        ) : (
                          '获取验证码'
                        )}
                      </Button>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={phoneLoginMutation.isPending}>
                {phoneLoginMutation.isPending ? '登录中...' : '登录'}
              </Button>
            </form>
          </Form>
        </TabsContent>

        {/* 密码登录 */}
        <TabsContent value="password" className="space-y-4">
          <Form {...passwordForm}>
            <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)} className="flex flex-col gap-4">
              <FormField
                control={passwordForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem className="grid gap-2">
                    <FormLabel>手机号码</FormLabel>
                    <FormControl>
                      <Input type="tel" placeholder="请输入您的手机号码" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={passwordForm.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="grid gap-2">
                    <FormLabel>密码</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="请输入密码" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={passwordForm.control}
                name="captchaVerified"
                render={() => (
                  <FormItem className="grid gap-2">
                    <FormLabel>滑块验证</FormLabel>
                    <FormControl>
                      <SliderCaptcha onVerify={handleCaptchaVerify} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={passwordLoginMutation.isPending || !passwordForm.watch('captchaVerified')}>
                {passwordLoginMutation.isPending ? '登录中...' : '登录'}
              </Button>
            </form>
          </Form>
        </TabsContent>
      </Tabs>

      <div className="text-center text-sm text-muted-foreground">
        <p>
          登录即表示您同意我们的{' '}
          <a href="#" className="text-primary hover:underline">
            服务条款
          </a>{' '}
          和{' '}
          <a href="#" className="text-primary hover:underline">
            隐私政策
          </a>
        </p>
      </div>
    </div>
  )
}
