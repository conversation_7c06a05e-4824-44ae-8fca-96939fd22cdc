'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Copy, Eye, EyeOff, Plus } from 'lucide-react'
import { trpc } from '@/lib/trpc'
import { toast } from 'sonner'
import type { Application } from '@server/db/schema'

const createApplicationSchema = z.object({
  name: z.string().min(1, '应用名称不能为空').max(10, '应用名称不能超过10个字符'),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
})

type CreateApplicationForm = z.infer<typeof createApplicationSchema>

interface CreateAppDialogProps {
  onSuccess?: () => void
}

export function CreateAppDialog({ onSuccess }: CreateAppDialogProps) {
  const [open, setOpen] = useState(false)
  const [createdApp, setCreatedApp] = useState<Application | null>(null)
  const [showSecret, setShowSecret] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm<CreateApplicationForm>({
    resolver: zodResolver(createApplicationSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  })

  const createApplicationMutation = trpc.application.create.useMutation({
    onSuccess: (data) => {
      toast.success('应用创建成功')
      setCreatedApp(data)
      onSuccess?.()
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  const onSubmit = (data: CreateApplicationForm) => {
    createApplicationMutation.mutate(data)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('已复制到剪贴板')
  }

  const handleClose = () => {
    setOpen(false)
    setCreatedApp(null)
    setShowSecret(false)
    reset()
  }

  const nameValue = watch('name')

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          创建应用
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        {createdApp ? (
          // 创建成功页面
          <>
            <DialogHeader>
              <DialogTitle className="text-green-600">应用创建成功！</DialogTitle>
              <DialogDescription>
                您的应用已成功创建，请妥善保存以下信息
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label>应用名称</Label>
                <div className="mt-1 p-3 bg-muted rounded-md">
                  {createdApp.name}
                </div>
              </div>

              <div>
                <Label>应用ID</Label>
                <div className="mt-1 flex items-center space-x-2">
                  <code className="flex-1 p-3 bg-muted rounded-md font-mono text-sm">
                    {createdApp.appId}
                  </code>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(createdApp.appId)}
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <div>
                <Label>应用密钥</Label>
                <div className="mt-1 flex items-center space-x-2">
                  <code className="flex-1 p-3 bg-muted rounded-md font-mono text-sm">
                    {showSecret ? createdApp.secret : '*'.repeat(32)}
                  </code>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowSecret(!showSecret)}
                  >
                    {showSecret ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </Button>
                  {showSecret && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(createdApp.secret)}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  )}
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  请妥善保存您的密钥，它将用于API调用认证
                </p>
              </div>

              <div className="flex space-x-2 pt-4">
                <Button onClick={handleClose} className="flex-1">
                  完成
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setCreatedApp(null)
                    setShowSecret(false)
                    reset()
                  }}
                >
                  继续创建
                </Button>
              </div>
            </div>
          </>
        ) : (
          // 创建表单
          <>
            <DialogHeader>
              <DialogTitle>创建应用</DialogTitle>
              <DialogDescription>
                输入应用名称来创建新应用，系统将自动生成应用ID和密钥
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  应用名称 <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="请输入应用名称（1-10字符）"
                  maxLength={10}
                />
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>{errors.name?.message}</span>
                  <span>{nameValue?.length || 0}/10</span>
                </div>
              </div>

              <div className="bg-muted p-3 rounded-lg">
                <h4 className="font-medium mb-2 text-sm">系统将自动生成：</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 应用ID：用于标识您的应用程序</li>
                  <li>• 应用密钥：用于API调用认证</li>
                  <li>• 初始余额：0.00 蚁贝</li>
                </ul>
              </div>

              <div className="flex space-x-2 pt-4">
                <Button
                  type="submit"
                  disabled={createApplicationMutation.isPending}
                  className="flex-1"
                >
                  {createApplicationMutation.isPending ? '创建中...' : '创建应用'}
                </Button>
                <Button type="button" variant="outline" onClick={handleClose}>
                  取消
                </Button>
              </div>
            </form>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
}
