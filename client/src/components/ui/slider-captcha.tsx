'use client'

import { useState, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Check } from 'lucide-react'

interface SliderCaptchaProps {
  onVerify: (verified: boolean) => void
  className?: string
}

export function SliderCaptcha({ onVerify, className }: SliderCaptchaProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [position, setPosition] = useState(0)
  const [isVerified, setIsVerified] = useState(false)
  const sliderRef = useRef<HTMLDivElement>(null)
  const trackRef = useRef<HTMLDivElement>(null)

  const handleMouseDown = (e: React.MouseEvent) => {
    if (isVerified) return
    setIsDragging(true)
    e.preventDefault()
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !trackRef.current || isVerified) return

    const rect = trackRef.current.getBoundingClientRect()
    const newPosition = Math.max(0, Math.min(e.clientX - rect.left - 20, rect.width - 40))
    setPosition(newPosition)

    // 当滑块接近右端时验证成功
    if (newPosition > rect.width - 60) {
      setIsVerified(true)
      setIsDragging(false)
      onVerify(true)
    }
  }

  const handleMouseUp = () => {
    if (!isVerified && position < (trackRef.current?.getBoundingClientRect().width || 0) - 60) {
      // 如果没有验证成功，重置位置
      setPosition(0)
    }
    setIsDragging(false)
  }

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isDragging, position])

  return (
    <div className={cn('w-full', className)}>
      <div
        ref={trackRef}
        className={cn(
          'relative h-10 bg-gray-100 border border-gray-300 rounded-md overflow-hidden select-none',
          isVerified && 'bg-green-100 border-green-300'
        )}
      >
        {/* 背景文字 */}
        <div className="absolute inset-0 flex items-center justify-center text-sm text-gray-500 pointer-events-none">
          {isVerified ? '验证成功' : '向右滑动完成验证'}
        </div>

        {/* 滑动进度条 */}
        <div
          className={cn(
            'absolute left-0 top-0 h-full transition-colors',
            isVerified ? 'bg-green-200' : 'bg-blue-200'
          )}
          style={{ width: `${position + 40}px` }}
        />

        {/* 滑块 */}
        <div
          ref={sliderRef}
          className={cn(
            'absolute top-0 w-10 h-10 bg-white border border-gray-300 rounded-md cursor-pointer flex items-center justify-center transition-all',
            isDragging && 'shadow-lg scale-105',
            isVerified && 'bg-green-500 border-green-500 text-white cursor-default'
          )}
          style={{ left: `${position}px` }}
          onMouseDown={handleMouseDown}
        >
          {isVerified ? (
            <Check className="w-4 h-4" />
          ) : (
            <div className="w-3 h-3 border-2 border-gray-400 border-r-transparent rounded-full" />
          )}
        </div>
      </div>
    </div>
  )
}
