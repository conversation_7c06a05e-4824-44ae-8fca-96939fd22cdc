// 模拟API数据，用于静态页面展示
export const mockApi = {
  user: {
    user: {
      useQuery: () => ({
        data: {
          id: '1',
          name: '张三',
          phone: '13800138000',
          avatar: '',
        },
        isLoading: false,
      }),
    },
  },
  auth: {
    phoneLogin: {
      useMutation: () => ({
        mutate: (data: any) => {
          console.log('Mock phone login:', data)
          // 模拟登录成功
          setTimeout(() => {
            window.location.href = '/'
          }, 1000)
        },
        isPending: false,
      }),
    },
    login: {
      useMutation: () => ({
        mutate: (data: any) => {
          console.log('Mock password login:', data)
          // 模拟登录成功
          setTimeout(() => {
            window.location.href = '/'
          }, 1000)
        },
        isPending: false,
      }),
    },
    sendSms: {
      useMutation: () => ({
        mutate: (data: any) => {
          console.log('Mock send SMS:', data)
          // 模拟发送验证码成功
        },
        isPending: false,
      }),
    },
  },
  application: {
    list: {
      useQuery: () => ({
        data: {
          items: [
            {
              id: '1',
              name: '测试应用1',
              appId: 'app_123456789',
              description: '这是一个测试应用',
              balance: 1000.50,
              traffic: 2.5,
              createdAt: new Date().toISOString(),
            },
            {
              id: '2',
              name: '测试应用2',
              appId: 'app_987654321',
              description: '另一个测试应用',
              balance: 500.25,
              traffic: 1.2,
              createdAt: new Date().toISOString(),
            },
          ],
          total: 2,
        },
        isLoading: false,
        refetch: () => Promise.resolve(),
      }),
    },
    byId: {
      useQuery: () => ({
        data: {
          id: '1',
          name: '测试应用1',
          appId: 'app_123456789',
          secret: 'sk_test_123456789abcdef',
          description: '这是一个测试应用',
          balance: 1000.50,
          traffic: 2.5,
          createdAt: new Date().toISOString(),
        },
        isLoading: false,
      }),
    },
    create: {
      useMutation: () => ({
        mutate: (data: any) => {
          console.log('Mock create application:', data)
          // 模拟创建成功
          const mockApp = {
            id: Date.now().toString(),
            name: data.name,
            appId: `app_${Date.now()}`,
            secret: `sk_test_${Date.now()}`,
            description: data.description || '',
            balance: 0,
            traffic: 0,
            createdAt: new Date().toISOString(),
          }
          return mockApp
        },
        isPending: false,
      }),
    },
    getApplicationStats: {
      useQuery: () => ({
        data: {
          accountCount: 150,
          trafficGB: 2.5,
          apiCallCount: 1250,
        },
        isLoading: false,
      }),
    },
    getApplicationTrends: {
      useQuery: () => ({
        data: Array.from({ length: 30 }, (_, i) => ({
          date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString(),
          apiCalls: Math.floor(Math.random() * 100) + 20,
          trafficGB: Math.random() * 0.5 + 0.1,
          accountCount: Math.floor(Math.random() * 10) + 5,
        })),
        isLoading: false,
      }),
    },
    regenerateSecret: {
      useMutation: () => ({
        mutate: (data: any) => {
          console.log('Mock regenerate secret:', data)
          return {
            secret: `sk_test_${Date.now()}`,
          }
        },
        isPending: false,
      }),
    },
  },
  balance: {
    getApplicationBalance: {
      useQuery: () => ({
        data: {
          balance: '1000.50',
        },
        isLoading: false,
      }),
    },
    getApplicationTransactions: {
      useQuery: () => ({
        data: {
          data: [
            {
              id: '1',
              type: 'RECHARGE',
              amount: '100.00',
              beforeBalance: '900.50',
              afterBalance: '1000.50',
              description: '充值',
              createdAt: new Date().toISOString(),
            },
            {
              id: '2',
              type: 'CONSUME',
              amount: '-10.00',
              beforeBalance: '910.50',
              afterBalance: '900.50',
              description: 'API调用消费',
              createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            },
          ],
        },
        isLoading: false,
      }),
    },
    getApiCalls: {
      useQuery: () => ({
        data: {
          data: [
            {
              id: '1',
              endpoint: '/api/v1/accounts',
              method: 'POST',
              costType: 'ACCOUNT_QUOTA',
              costAmount: '40.00',
              statusCode: 200,
              createdAt: new Date().toISOString(),
            },
            {
              id: '2',
              endpoint: '/api/v1/traffic',
              method: 'GET',
              costType: 'TRAFFIC',
              costAmount: '1.50',
              statusCode: 200,
              createdAt: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
            },
          ],
        },
        isLoading: false,
      }),
    },
  },
}
